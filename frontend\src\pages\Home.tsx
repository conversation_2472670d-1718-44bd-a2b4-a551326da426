import React from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout';
import { useAuth } from '../contexts/AuthContext';
import { UserRole } from '../services/userService';
import {
  FiCalendar,
  FiMail,
  FiPieChart,
  FiUsers,
  FiGlobe,
  FiSettings,
  FiCheckCircle,
  FiClock,
  FiSmartphone
} from 'react-icons/fi';

const Home: React.FC = () => {
  const { isAuthenticated, user } = useAuth();

  return (
    <Layout>
      <div className="max-w-6xl mx-auto px-4">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-3xl bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-700 text-white my-8">
          <div className="absolute inset-0 opacity-10">
            <svg className="h-full w-full" viewBox="0 0 800 800">
              <path d="M435.5,160.5Q432,321,271,329Q110,337,97.5,168.5Q85,0,272.5,0Q460,0,447.5,80Q435,160,435.5,160.5Z" fill="white"></path>
              <path d="M215.5,279.5Q142,559,71,279.5Q0,0,357.5,0Q715,0,465,279.5Q215,559,215.5,279.5Z" fill="white"></path>
            </svg>
          </div>

          <div className="relative z-10 flex flex-col md:flex-row items-center px-6 py-16 md:py-24 md:px-12">
            <div className="md:w-1/2 text-center md:text-left mb-10 md:mb-0">
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight">
                Simplify Your <span className="text-yellow-300">Scheduling</span> Experience
              </h1>
              <p className="text-xl text-blue-100 mb-10 max-w-xl">
                Scheduly helps businesses manage appointments, reduce no-shows, and provide a seamless booking experience for clients.
              </p>

              {!isAuthenticated ? (
                <div className="flex flex-col sm:flex-row justify-center md:justify-start gap-4">
                  <Link
                    to="/register"
                    className="bg-white text-blue-700 px-8 py-4 rounded-lg font-medium hover:bg-blue-50 transition-all transform hover:-translate-y-1 shadow-lg inline-block"
                  >
                    Get Started Free
                  </Link>
                  <Link
                    to="/pricing"
                    className="bg-transparent text-white border-2 border-white px-8 py-4 rounded-lg font-medium hover:bg-white/10 transition-all"
                  >
                    View Pricing
                  </Link>
                </div>
              ) : (
                <div className="flex justify-center md:justify-start">
                  <Link
                    to={
                      user?.role === UserRole.CLIENT
                        ? '/book'
                        : user?.role === UserRole.ADMIN || user?.role === UserRole.STAFF
                        ? '/dashboard'
                        : user?.role === UserRole.SUPER_ADMIN
                        ? '/super-admin/dashboard'
                        : '/'
                    }
                    className="bg-white text-blue-700 px-8 py-4 rounded-lg font-medium hover:bg-blue-50 transition-all transform hover:-translate-y-1 shadow-lg inline-block"
                  >
                    {user?.role === UserRole.CLIENT
                      ? 'Book an Appointment'
                      : user?.role === UserRole.SUPER_ADMIN
                      ? 'Go to Super Admin Dashboard'
                      : 'Go to Dashboard'}
                  </Link>
                </div>
              )}

              <div className="mt-8 flex flex-wrap justify-center md:justify-start gap-6">
                <div className="flex items-center">
                  <FiCheckCircle className="text-yellow-300 mr-2" />
                  <span className="text-sm">No credit card required</span>
                </div>
                <div className="flex items-center">
                  <FiCheckCircle className="text-yellow-300 mr-2" />
                  <span className="text-sm">14-day free trial</span>
                </div>
                <div className="flex items-center">
                  <FiCheckCircle className="text-yellow-300 mr-2" />
                  <span className="text-sm">Cancel anytime</span>
                </div>
              </div>
            </div>

            <div className="md:w-1/2 flex justify-center">
              <div className="relative w-full max-w-md">
                <div className="absolute inset-0 bg-blue-500 rounded-xl opacity-20 blur-xl transform -rotate-6"></div>
                <div className="relative bg-white rounded-xl shadow-2xl overflow-hidden">
                  <div className="bg-blue-600 text-white p-4">
                    <div className="flex justify-between items-center">
                      <h3 className="font-bold">Appointment Calendar</h3>
                      <FiCalendar />
                    </div>
                  </div>
                  <div className="p-6 bg-white text-gray-800">
                    <div className="grid grid-cols-7 gap-1 text-center text-xs mb-2">
                      <div>Su</div>
                      <div>Mo</div>
                      <div>Tu</div>
                      <div>We</div>
                      <div>Th</div>
                      <div>Fr</div>
                      <div>Sa</div>
                    </div>
                    <div className="grid grid-cols-7 gap-1 text-center">
                      {[...Array(31)].map((_, i) => (
                        <div
                          key={i}
                          className={`p-2 rounded-full ${i === 14 ? 'bg-blue-600 text-white' : i % 7 === 0 || i % 7 === 6 ? 'text-gray-400' : ''}`}
                        >
                          {i + 1}
                        </div>
                      ))}
                    </div>
                    <div className="mt-4 space-y-2">
                      <div className="p-3 bg-blue-50 rounded-lg flex items-center">
                        <div className="w-2 h-10 bg-blue-600 rounded-full mr-3"></div>
                        <div>
                          <div className="flex items-center text-sm">
                            <FiClock className="mr-1 text-blue-600" />
                            <span>9:00 AM - 10:00 AM</span>
                          </div>
                          <div className="font-medium">Haircut Appointment</div>
                        </div>
                      </div>
                      <div className="p-3 bg-purple-50 rounded-lg flex items-center">
                        <div className="w-2 h-10 bg-purple-600 rounded-full mr-3"></div>
                        <div>
                          <div className="flex items-center text-sm">
                            <FiClock className="mr-1 text-purple-600" />
                            <span>2:00 PM - 3:00 PM</span>
                          </div>
                          <div className="font-medium">Massage Therapy</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Features Section */}
        <div className="py-16 md:py-24">
          <div className="text-center mb-16">
            <span className="bg-blue-100 text-blue-700 px-4 py-1 rounded-full text-sm font-medium inline-block mb-4">POWERFUL FEATURES</span>
            <h2 className="text-3xl md:text-5xl font-bold text-gray-800 mb-6">
              Everything You Need to <span className="text-blue-600">Streamline</span> Your Business
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our comprehensive suite of tools helps you manage appointments, clients, and your entire business efficiently
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
              <div className="text-blue-600 mb-6 flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full">
                <FiCalendar className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Online Booking</h3>
              <p className="text-gray-600">
                Allow clients to book appointments online 24/7, reducing phone calls and saving time. Customizable booking pages for your business.
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <Link to="/register" className="text-blue-600 font-medium flex items-center hover:text-blue-800">
                  Learn more <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path></svg>
                </Link>
              </div>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
              <div className="text-green-600 mb-6 flex items-center justify-center w-16 h-16 bg-green-100 rounded-full">
                <FiMail className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Automated Reminders</h3>
              <p className="text-gray-600">
                Send automated email and SMS reminders to reduce no-shows and last-minute cancellations. Customize timing and content.
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <Link to="/register" className="text-blue-600 font-medium flex items-center hover:text-blue-800">
                  Learn more <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path></svg>
                </Link>
              </div>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
              <div className="text-purple-600 mb-6 flex items-center justify-center w-16 h-16 bg-purple-100 rounded-full">
                <FiPieChart className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Business Analytics</h3>
              <p className="text-gray-600">
                Gain insights into your business with analytics on appointments, popular services, revenue, and staff performance.
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <Link to="/register" className="text-blue-600 font-medium flex items-center hover:text-blue-800">
                  Learn more <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path></svg>
                </Link>
              </div>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
              <div className="text-indigo-600 mb-6 flex items-center justify-center w-16 h-16 bg-indigo-100 rounded-full">
                <FiUsers className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Client Management</h3>
              <p className="text-gray-600">
                Maintain a comprehensive database of clients, including contact information, appointment history, and notes.
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <Link to="/register" className="text-blue-600 font-medium flex items-center hover:text-blue-800">
                  Learn more <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path></svg>
                </Link>
              </div>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
              <div className="text-red-600 mb-6 flex items-center justify-center w-16 h-16 bg-red-100 rounded-full">
                <FiGlobe className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Google Calendar Integration</h3>
              <p className="text-gray-600">
                Sync appointments with Google Calendar to avoid double-bookings and manage your schedule from anywhere.
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <Link to="/register" className="text-blue-600 font-medium flex items-center hover:text-blue-800">
                  Learn more <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path></svg>
                </Link>
              </div>
            </div>

            <div className="bg-white p-8 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 border border-gray-100">
              <div className="text-yellow-600 mb-6 flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-full">
                <FiSettings className="h-8 w-8" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-gray-800">Customizable Settings</h3>
              <p className="text-gray-600">
                Configure business hours, service offerings, staff availability, and booking policies to match your needs.
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <Link to="/register" className="text-blue-600 font-medium flex items-center hover:text-blue-800">
                  Learn more <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7"></path></svg>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* How It Works Section */}
        <div className="py-20 my-16 bg-gradient-to-b from-white to-blue-50 rounded-3xl">
          <div className="text-center mb-16">
            <span className="bg-indigo-100 text-indigo-700 px-4 py-1 rounded-full text-sm font-medium inline-block mb-4">SIMPLE PROCESS</span>
            <h2 className="text-3xl md:text-5xl font-bold text-gray-800 mb-6">
              Get Started in <span className="text-blue-600">Minutes</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our intuitive platform makes it easy to set up and start accepting appointments right away
            </p>
          </div>

          <div className="max-w-6xl mx-auto px-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {/* Step 1 */}
              <div className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative">
                <div className="absolute -top-5 -left-5 w-12 h-12 rounded-full bg-blue-600 text-white flex items-center justify-center text-xl font-bold">1</div>
                <div className="text-blue-600 mb-6 flex items-center justify-center w-20 h-20 bg-blue-100 rounded-full mx-auto">
                  <FiUsers className="h-10 w-10" />
                </div>
                <h3 className="text-xl font-bold mb-4 text-center text-gray-800">Create Your Account</h3>
                <p className="text-gray-600 text-center">
                  Sign up for Scheduly and set up your business profile with services, staff, and business hours.
                </p>
                <div className="mt-6 text-center">
                  <Link to="/register" className="text-blue-600 font-medium hover:text-blue-800">
                    Create account →
                  </Link>
                </div>
              </div>

              {/* Step 2 */}
              <div className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative">
                <div className="absolute -top-5 -left-5 w-12 h-12 rounded-full bg-blue-600 text-white flex items-center justify-center text-xl font-bold">2</div>
                <div className="text-blue-600 mb-6 flex items-center justify-center w-20 h-20 bg-blue-100 rounded-full mx-auto">
                  <FiSettings className="h-10 w-10" />
                </div>
                <h3 className="text-xl font-bold mb-4 text-center text-gray-800">Customize Settings</h3>
                <p className="text-gray-600 text-center">
                  Configure your booking page, notification settings, and connect your Google Calendar.
                </p>
                <div className="mt-6 text-center">
                  <Link to="/register" className="text-blue-600 font-medium hover:text-blue-800">
                    Learn more →
                  </Link>
                </div>
              </div>

              {/* Step 3 */}
              <div className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative">
                <div className="absolute -top-5 -left-5 w-12 h-12 rounded-full bg-blue-600 text-white flex items-center justify-center text-xl font-bold">3</div>
                <div className="text-blue-600 mb-6 flex items-center justify-center w-20 h-20 bg-blue-100 rounded-full mx-auto">
                  <FiGlobe className="h-10 w-10" />
                </div>
                <h3 className="text-xl font-bold mb-4 text-center text-gray-800">Share Booking Page</h3>
                <p className="text-gray-600 text-center">
                  Share your unique booking link with clients via email, social media, or your website.
                </p>
                <div className="mt-6 text-center">
                  <Link to="/register" className="text-blue-600 font-medium hover:text-blue-800">
                    Learn more →
                  </Link>
                </div>
              </div>

              {/* Step 4 */}
              <div className="bg-white p-8 rounded-xl shadow-lg border border-blue-100 relative">
                <div className="absolute -top-5 -left-5 w-12 h-12 rounded-full bg-blue-600 text-white flex items-center justify-center text-xl font-bold">4</div>
                <div className="text-blue-600 mb-6 flex items-center justify-center w-20 h-20 bg-blue-100 rounded-full mx-auto">
                  <FiPieChart className="h-10 w-10" />
                </div>
                <h3 className="text-xl font-bold mb-4 text-center text-gray-800">Manage Business</h3>
                <p className="text-gray-600 text-center">
                  Track appointments, manage clients, and analyze your business performance all in one place.
                </p>
                <div className="mt-6 text-center">
                  <Link to="/register" className="text-blue-600 font-medium hover:text-blue-800">
                    Learn more →
                  </Link>
                </div>
              </div>
            </div>

            <div className="mt-16 text-center">
              <Link
                to="/register"
                className="bg-blue-600 text-white px-8 py-4 rounded-lg font-medium hover:bg-blue-700 transition-all transform hover:-translate-y-1 shadow-lg inline-block"
              >
                Start Your Free Trial
              </Link>
              <p className="mt-4 text-gray-600">No credit card required • 14-day free trial • Cancel anytime</p>
            </div>
          </div>
        </div>

        {/* Testimonials Section */}
        <div className="py-20 my-16 bg-gray-50 rounded-3xl">
          <div className="text-center mb-16">
            <span className="bg-green-100 text-green-700 px-4 py-1 rounded-full text-sm font-medium inline-block mb-4">TESTIMONIALS</span>
            <h2 className="text-3xl md:text-5xl font-bold text-gray-800 mb-6">
              Trusted by <span className="text-blue-600">Thousands</span> of Businesses
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              See what our customers have to say about how Scheduly has transformed their scheduling
            </p>
          </div>

          <div className="max-w-6xl mx-auto px-6">
            <div className="grid md:grid-cols-3 gap-8">
              <div className="bg-white p-8 rounded-xl shadow-lg border border-gray-100 transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl">
                <div className="flex items-center mb-6">
                  <div className="text-yellow-400 flex">
                    {[...Array(5)].map((_, i) => (
                      <svg key={i} className="w-5 h-5 fill-current" viewBox="0 0 24 24">
                        <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                      </svg>
                    ))}
                  </div>
                </div>
                <p className="text-gray-600 mb-6 italic">
                  "Scheduly has transformed how we manage our salon. The online booking system has reduced phone calls by 70% and our clients love the convenience."
                </p>
                <div className="flex items-center">
                  <div className="w-14 h-14 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-bold mr-4">
                    JS
                  </div>
                  <div>
                    <p className="font-bold text-gray-800">Jennifer Smith</p>
                    <p className="text-gray-500 text-sm">Bella Salon</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-8 rounded-xl shadow-lg border border-gray-100 transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl">
                <div className="flex items-center mb-6">
                  <div className="text-yellow-400 flex">
                    {[...Array(5)].map((_, i) => (
                      <svg key={i} className="w-5 h-5 fill-current" viewBox="0 0 24 24">
                        <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                      </svg>
                    ))}
                  </div>
                </div>
                <p className="text-gray-600 mb-6 italic">
                  "The Google Calendar integration is seamless. I can manage all my appointments in one place and never worry about double-bookings again."
                </p>
                <div className="flex items-center">
                  <div className="w-14 h-14 rounded-full bg-green-100 flex items-center justify-center text-green-600 font-bold mr-4">
                    MJ
                  </div>
                  <div>
                    <p className="font-bold text-gray-800">Michael Johnson</p>
                    <p className="text-gray-500 text-sm">Fitness Coach</p>
                  </div>
                </div>
              </div>

              <div className="bg-white p-8 rounded-xl shadow-lg border border-gray-100 transform transition-all duration-300 hover:-translate-y-2 hover:shadow-xl">
                <div className="flex items-center mb-6">
                  <div className="text-yellow-400 flex">
                    {[...Array(5)].map((_, i) => (
                      <svg key={i} className="w-5 h-5 fill-current" viewBox="0 0 24 24">
                        <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                      </svg>
                    ))}
                  </div>
                </div>
                <p className="text-gray-600 mb-6 italic">
                  "The client management features have helped us build stronger relationships with our patients. We can track their history and preferences easily."
                </p>
                <div className="flex items-center">
                  <div className="w-14 h-14 rounded-full bg-purple-100 flex items-center justify-center text-purple-600 font-bold mr-4">
                    SD
                  </div>
                  <div>
                    <p className="font-bold text-gray-800">Dr. Sarah Davis</p>
                    <p className="text-gray-500 text-sm">Wellness Clinic</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* FAQ Section */}
        <div className="py-16 my-16">
          <div className="text-center mb-16">
            <span className="bg-purple-100 text-purple-700 px-4 py-1 rounded-full text-sm font-medium inline-block mb-4">FAQ</span>
            <h2 className="text-3xl md:text-5xl font-bold text-gray-800 mb-6">
              Frequently Asked <span className="text-blue-600">Questions</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Everything you need to know about Scheduly
            </p>
          </div>

          <div className="max-w-4xl mx-auto px-6">
            <div className="space-y-6">
              <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
                <h3 className="text-xl font-bold text-gray-800 mb-3">How much does Scheduly cost?</h3>
                <p className="text-gray-600">
                  Scheduly offers flexible pricing plans starting at $19.99/month. We also offer a 14-day free trial with no credit card required, so you can try all features before committing.
                </p>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
                <h3 className="text-xl font-bold text-gray-800 mb-3">Can I integrate with Google Calendar?</h3>
                <p className="text-gray-600">
                  Yes! Scheduly offers seamless two-way integration with Google Calendar. This means appointments booked in Scheduly will appear in your Google Calendar and vice versa, helping you avoid double-bookings.
                </p>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
                <h3 className="text-xl font-bold text-gray-800 mb-3">How do clients book appointments?</h3>
                <p className="text-gray-600">
                  Clients can book appointments through your personalized booking page. You can share this link on your website, social media, or via email. Clients select their preferred service, date, and time, and receive instant confirmation.
                </p>
              </div>

              <div className="bg-white p-6 rounded-xl shadow-md border border-gray-100">
                <h3 className="text-xl font-bold text-gray-800 mb-3">Can I customize the booking page?</h3>
                <p className="text-gray-600">
                  Absolutely! You can customize your booking page with your logo, brand colors, and welcome message to provide a consistent brand experience for your clients.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* CTA Section */}
        <div className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-700 rounded-3xl shadow-xl my-16 p-8 md:p-16">
          <div className="absolute inset-0 opacity-10">
            <svg className="h-full w-full" viewBox="0 0 800 800">
              <path d="M435.5,160.5Q432,321,271,329Q110,337,97.5,168.5Q85,0,272.5,0Q460,0,447.5,80Q435,160,435.5,160.5Z" fill="white"></path>
              <path d="M215.5,279.5Q142,559,71,279.5Q0,0,357.5,0Q715,0,465,279.5Q215,559,215.5,279.5Z" fill="white"></path>
            </svg>
          </div>

          <div className="relative flex flex-col md:flex-row items-center justify-between">
            <div className="md:w-2/3 mb-8 md:mb-0 text-center md:text-left">
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-white">Ready to transform your scheduling?</h2>
              <p className="text-xl md:text-2xl mb-0 max-w-3xl text-blue-100">
                Join thousands of businesses that use Scheduly to manage their appointments and grow their business.
              </p>
            </div>

            <div className="md:w-1/3 text-center md:text-right">
              <div className="flex flex-col sm:flex-row justify-center md:justify-end gap-4">
                <Link
                  to="/register"
                  className="bg-white text-blue-700 px-8 py-4 rounded-lg font-medium hover:bg-blue-50 transition-all transform hover:-translate-y-1 shadow-lg inline-block"
                >
                  Get Started for Free
                </Link>
                <Link
                  to="/pricing"
                  className="bg-transparent text-white border-2 border-white px-8 py-4 rounded-lg font-medium hover:bg-white/10 transition-all inline-block"
                >
                  View Pricing
                </Link>
              </div>
              <p className="mt-4 text-blue-200">No credit card required • 14-day free trial</p>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default Home;
