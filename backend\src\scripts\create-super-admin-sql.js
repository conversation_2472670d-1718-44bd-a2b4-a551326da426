// This script creates a super admin user using direct SQL queries
// Run with: node create-super-admin-sql.js

const { Pool } = require('pg');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

// Database connection
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'appointy',
  password: process.env.DB_PASSWORD || 'postgres',
  port: process.env.DB_PORT || 5432,
});

// Super admin details - customize these as needed
const superAdmin = {
  id: uuidv4(),
  firstName: 'Super',
  lastName: 'Admin',
  email: '<EMAIL>',
  password: 'SuperAdmin123!', // Change this to a secure password
  role: 'super_admin',
};

async function createSuperAdmin() {
  const client = await pool.connect();
  
  try {
    console.log('Creating a super admin user...');
    
    // First, modify the tenantId column to allow NULL values
    await client.query(`
      ALTER TABLE users ALTER COLUMN "tenantId" DROP NOT NULL;
    `);
    
    console.log('Modified users table to allow NULL tenantId');
    
    // Check if super admin already exists
    const checkQuery = `
      SELECT * FROM users 
      WHERE email = $1 AND role = $2
    `;
    
    const checkResult = await client.query(checkQuery, [superAdmin.email, superAdmin.role]);
    
    if (checkResult.rows.length > 0) {
      console.log('Super admin already exists. Updating password...');
      
      // Hash password
      const hashedPassword = await bcrypt.hash(superAdmin.password, 10);
      
      // Update user
      const updateQuery = `
        UPDATE users 
        SET password = $1, active = true 
        WHERE email = $2 AND role = $3
        RETURNING id, email, role
      `;
      
      const updateResult = await client.query(updateQuery, [
        hashedPassword, 
        superAdmin.email, 
        superAdmin.role
      ]);
      
      console.log('Super admin updated:', {
        id: updateResult.rows[0].id,
        email: updateResult.rows[0].email,
        role: updateResult.rows[0].role,
      });
    } else {
      // Hash password
      const hashedPassword = await bcrypt.hash(superAdmin.password, 10);
      
      // Create user - note: super admin doesn't need a tenantId
      const insertQuery = `
        INSERT INTO users (
          id, 
          "tenantId", 
          "firstName", 
          "lastName", 
          email, 
          password, 
          role, 
          active, 
          "createdAt", 
          "updatedAt"
        ) 
        VALUES ($1, NULL, $2, $3, $4, $5, $6, true, NOW(), NOW())
        RETURNING id, email, role
      `;
      
      const insertResult = await client.query(insertQuery, [
        superAdmin.id,
        superAdmin.firstName,
        superAdmin.lastName,
        superAdmin.email,
        hashedPassword,
        superAdmin.role
      ]);
      
      console.log('Super admin created:', {
        id: insertResult.rows[0].id,
        email: insertResult.rows[0].email,
        role: insertResult.rows[0].role,
      });
    }
    
    console.log('\nSuper admin login credentials:');
    console.log('Email:', superAdmin.email);
    console.log('Password:', superAdmin.password);
    console.log('\nPlease change the password after first login for security reasons.');
    
  } catch (error) {
    console.error('Error creating super admin:', error);
  } finally {
    client.release();
    pool.end();
  }
}

// Run the function
createSuperAdmin();
