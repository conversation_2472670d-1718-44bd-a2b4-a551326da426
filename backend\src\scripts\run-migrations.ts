import { supabase } from '../config/supabase';
import * as fs from 'fs';
import * as path from 'path';

async function runMigrations() {
  console.log('Running migrations...');

  try {
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../migrations/create_execute_sql_function.sql');
    const sql = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL directly using Supabase's REST API
    const { error } = await supabase.rpc('execute_sql', { query: sql });

    if (error) {
      console.error('Error running migration:', error);
      
      // If the function doesn't exist yet, we need to create it first
      // This is a bootstrap problem - we can use a direct query for the initial creation
      console.log('Attempting to create function directly...');
      
      const { error: directError } = await supabase.rpc('exec_sql', { sql });
      
      if (directError) {
        console.error('Failed to create function directly:', directError);
        process.exit(1);
      } else {
        console.log('Function created successfully via direct query.');
      }
    } else {
      console.log('Migration completed successfully.');
    }
  } catch (error) {
    console.error('Error running migrations:', error);
    process.exit(1);
  }
}

// Run the migrations
runMigrations().catch(console.error);
