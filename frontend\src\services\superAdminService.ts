import api from './api';

interface DashboardData {
  stats: {
    totalTenants: number;
    activeTenants: number;
    totalUsers: number;
    totalAppointments: number;
  };
  recentTenants: {
    id: string;
    name: string;
    email: string;
    createdAt: string;
    active: boolean;
  }[];
}

interface Tenant {
  id: string;
  name: string;
  email: string;
  phone: string;
  address: string;
  active: boolean;
  createdAt: string;
}

interface TenantDetails {
  tenant: Tenant & {
    timezone?: string;
    currency?: string;
  };
  stats: {
    users: number;
    appointments: number;
    services: number;
  };
}

const superAdminService = {
  // Get super admin dashboard data
  getDashboardData: async (): Promise<DashboardData> => {
    const response = await api.get('/super-admin/dashboard');
    return response.data.data;
  },

  // Get all tenants (businesses)
  getAllTenants: async (): Promise<Tenant[]> => {
    const response = await api.get('/super-admin/tenants');
    return response.data.data;
  },

  // Get tenant details
  getTenantDetails: async (tenantId: string): Promise<TenantDetails> => {
    const response = await api.get(`/super-admin/tenants/${tenantId}`);
    return response.data.data;
  },

  // Update tenant status (activate/deactivate)
  updateTenantStatus: async (tenantId: string, active: boolean): Promise<Tenant> => {
    const response = await api.put(`/super-admin/tenants/${tenantId}/status`, { active });
    return response.data.data;
  },

  // Delete tenant
  deleteTenant: async (tenantId: string): Promise<void> => {
    await api.delete(`/super-admin/tenants/${tenantId}`);
  }
};

export default superAdminService;
