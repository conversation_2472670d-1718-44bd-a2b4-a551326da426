import { Request, Response } from 'express';
import { UserRole } from '../models/supabase';
import { Tenant, Service, User, StaffService } from '../utils/modelHelpers';
import { Service as ServiceType, User as UserType } from '../models/supabase';

// Get all services for a tenant
export const getServices = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;

    const services = await Service.findAll({
      where: {
        tenantId,
        active: true,
      },
    });

    res.json(services);
  } catch (error) {
    console.error('Get services error:', error);
    res.status(500).json({ message: 'Failed to get services' });
  }
};

// Get service by ID
export const getServiceById = async (req: Request, res: Response) => {
  try {
    const { tenantId, serviceId } = req.params;

    const service = await Service.findOne({
      where: {
        id: serviceId,
        tenantId,
      },
      include: [
        {
          model: 'users',
          as: 'staff',
          attributes: ['id', 'firstName', 'lastName'],
        },
      ],
    });

    if (!service) {
      return res.status(404).json({ message: 'Service not found' });
    }

    res.json(service);
  } catch (error) {
    console.error('Get service error:', error);
    res.status(500).json({ message: 'Failed to get service' });
  }
};

// Create a new service
export const createService = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.params;
    const { name, description, duration, price, color } = req.body;

    // Check if user has admin access to this tenant
    if (req.user.tenantId !== tenantId || req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to create services for this tenant' });
    }

    // Create new service
    const serviceData: Partial<ServiceType> = {
      tenantId,
      name,
      description,
      duration,
      price,
      color,
      active: true,
    };

    const service = await Service.create(serviceData);

    // Convert to ServiceType to access properties
    const serviceResponse = service as unknown as ServiceType;

    res.status(201).json({
      message: 'Service created successfully',
      service: {
        id: serviceResponse.id,
        name: serviceResponse.name,
        description: serviceResponse.description,
        duration: serviceResponse.duration,
        price: serviceResponse.price,
        color: serviceResponse.color,
        active: serviceResponse.active
      },
    });
  } catch (error) {
    console.error('Create service error:', error);
    res.status(500).json({ message: 'Failed to create service' });
  }
};

// Update a service
export const updateService = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId, serviceId } = req.params;
    const { name, description, duration, price, color, active } = req.body;

    // Check if user has admin access to this tenant
    if (req.user.tenantId !== tenantId || req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to update services for this tenant' });
    }

    const service = await Service.findOne({
      where: {
        id: serviceId,
        tenantId,
      },
    });

    if (!service) {
      return res.status(404).json({ message: 'Service not found' });
    }

    // Convert to ServiceType to access properties
    const serviceData = service as unknown as ServiceType;

    // Update service
    const updateData: Partial<ServiceType> = {
      name: name || serviceData.name,
      description: description !== undefined ? description : serviceData.description,
      duration: duration || serviceData.duration,
      price: price !== undefined ? price : serviceData.price,
      color: color || serviceData.color,
      active: active !== undefined ? active : serviceData.active,
    };

    const updatedService = await Service.update(
      updateData,
      {
        where: {
          id: serviceId
        }
      }
    );

    res.json({
      message: 'Service updated successfully',
      service: updatedService,
    });
  } catch (error) {
    console.error('Update service error:', error);
    res.status(500).json({ message: 'Failed to update service' });
  }
};

// Delete a service
export const deleteService = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId, serviceId } = req.params;

    // Check if user has admin access to this tenant
    if (req.user.tenantId !== tenantId || req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to delete services for this tenant' });
    }

    const service = await Service.findOne({
      where: {
        id: serviceId,
        tenantId,
      },
    });

    if (!service) {
      return res.status(404).json({ message: 'Service not found' });
    }

    // Instead of hard delete, set active to false
    await Service.update(
      { active: false },
      {
        where: {
          id: serviceId
        }
      }
    );

    res.json({ message: 'Service deactivated successfully' });
  } catch (error) {
    console.error('Delete service error:', error);
    res.status(500).json({ message: 'Failed to delete service' });
  }
};

// Assign staff to service
export const assignStaffToService = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId, serviceId } = req.params;
    const { staffIds } = req.body;

    // Check if user has admin access to this tenant
    if (req.user.tenantId !== tenantId || req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to assign staff to services for this tenant' });
    }

    // Check if service exists
    const service = await Service.findOne({
      where: {
        id: serviceId,
        tenantId,
      },
    });

    if (!service) {
      return res.status(404).json({ message: 'Service not found' });
    }

    // Check if all staff members exist and belong to this tenant
    const staff = await User.findAll({
      where: {
        id: staffIds,
        tenantId,
        role: [UserRole.ADMIN, UserRole.STAFF],
      },
    });

    if (staff.length !== staffIds.length) {
      return res.status(400).json({ message: 'One or more staff members not found' });
    }

    // Remove existing assignments
    await StaffService.destroy({
      where: {
        serviceId,
      },
    });

    // Create new assignments
    const assignments = await Promise.all(
      staffIds.map(async (staffId: string) => {
        // Create a new staff-service assignment
        return StaffService.create({
          staffId,
          serviceId,
        });
      })
    );

    res.json({
      message: 'Staff assigned to service successfully',
      assignments: assignments.length,
    });
  } catch (error) {
    console.error('Assign staff to service error:', error);
    res.status(500).json({ message: 'Failed to assign staff to service' });
  }
};
