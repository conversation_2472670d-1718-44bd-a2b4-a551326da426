-- Create a function to execute SQL queries safely
-- This function should be restricted to authenticated users with appropriate permissions
CREATE OR REPLACE FUNCTION execute_sql(query text)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER -- Run with the privileges of the function creator
AS $$
DECLARE
  result JSONB;
BEGIN
  -- Execute the query and convert the result to JSON
  EXECUTE 'SELECT json_agg(t) FROM (' || query || ') t' INTO result;
  RETURN result;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION execute_sql TO authenticated;

-- Comment on function
COMMENT ON FUNCTION execute_sql IS 'Executes a SQL query and returns the result as JSO<PERSON>. This function should be used with caution and only with parameterized queries to prevent SQL injection.';
