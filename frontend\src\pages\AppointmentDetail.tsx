import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import Layout from '../components/Layout';
import { useAuth } from '../contexts/AuthContext';
import { useCurrency } from '../contexts/CurrencyContext';
import { useTimezone } from '../contexts/TimezoneContext';
import { appointmentService, AppointmentStatus } from '../services';
import Modal from '../components/Modal';

const AppointmentDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { tenant } = useAuth();
  const { formatPrice } = useCurrency();
  const { formatDate: formatDateWithTimezone, formatTime: formatTimeWithTimezone } = useTimezone();
  const navigate = useNavigate();

  const [appointment, setAppointment] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isCancelModalOpen, setIsCancelModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
  const [newStatus, setNewStatus] = useState<AppointmentStatus | ''>('');

  useEffect(() => {
    const fetchAppointment = async () => {
      if (!tenant || !id) return;

      setIsLoading(true);
      setError(null);

      try {
        console.log('Fetching appointment with ID:', id);
        const data = await appointmentService.getAppointmentById(tenant.id, id);
        console.log('Appointment data received:', data);
        setAppointment(data);
      } catch (err: any) {
        console.error('Error fetching appointment:', err);
        setError('Failed to load appointment details. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAppointment();
  }, [tenant, id]);

  const formatDate = (dateString: string) => {
    return formatDateWithTimezone(dateString);
  };

  const formatTime = (dateString: string) => {
    return formatTimeWithTimezone(dateString);
  };

  const handleStatusChange = async () => {
    if (!tenant || !id || !newStatus) return;

    try {
      console.log('Updating appointment status:', { tenantId: tenant.id, appointmentId: id, status: newStatus });
      await appointmentService.updateAppointment(tenant.id, id, { status: newStatus as AppointmentStatus });

      // Update the appointment in state
      setAppointment({ ...appointment, status: newStatus });

      // Show success message
      setSuccess(`Appointment status updated to ${newStatus}`);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);

      // Close the modal
      setIsStatusModalOpen(false);
    } catch (err: any) {
      console.error('Error updating appointment status:', err);
      setError('Failed to update appointment status. Please try again later.');
    }
  };

  const handleCancelAppointment = async () => {
    if (!tenant || !id) return;

    try {
      await appointmentService.cancelAppointment(tenant.id, id);

      // Update the appointment in state
      setAppointment({ ...appointment, status: AppointmentStatus.CANCELLED });

      // Show success message
      setSuccess('Appointment cancelled successfully');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);

      // Close the modal
      setIsCancelModalOpen(false);
    } catch (err: any) {
      console.error('Error cancelling appointment:', err);
      setError('Failed to cancel appointment. Please try again later.');
    }
  };

  const handleDeleteAppointment = async () => {
    if (!tenant || !id) return;

    try {
      await appointmentService.deleteAppointment(tenant.id, id);

      // Show success message
      setSuccess('Appointment deleted successfully');

      // Navigate back to appointments list after a short delay
      setTimeout(() => {
        navigate('/appointments');
      }, 1500);

      // Close the modal
      setIsDeleteModalOpen(false);
    } catch (err: any) {
      console.error('Error deleting appointment:', err);
      setError('Failed to delete appointment. Please try again later.');
    }
  };

  const getStatusBadgeClass = (status: AppointmentStatus) => {
    switch (status) {
      case AppointmentStatus.CONFIRMED:
        return 'bg-green-100 text-green-800';
      case AppointmentStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800';
      case AppointmentStatus.CANCELLED:
        return 'bg-red-100 text-red-800';
      case AppointmentStatus.COMPLETED:
        return 'bg-blue-100 text-blue-800';
      case AppointmentStatus.NO_SHOW:
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto">
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            {error}
          </div>
          <button
            onClick={() => navigate('/appointments')}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Back to Appointments
          </button>
        </div>
      </Layout>
    );
  }

  if (!appointment) {
    return (
      <Layout>
        <div className="max-w-4xl mx-auto">
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6">
            Appointment not found
          </div>
          <button
            onClick={() => navigate('/appointments')}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Back to Appointments
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800">Appointment Details</h1>
          <div className="flex space-x-2">
            <Link
              to={`/appointments/${id}/edit`}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            >
              Edit
            </Link>
            <button
              onClick={() => setIsCancelModalOpen(true)}
              className="bg-yellow-600 text-white px-4 py-2 rounded-md hover:bg-yellow-700"
              disabled={appointment.status === AppointmentStatus.CANCELLED}
            >
              Cancel
            </button>
            <button
              onClick={() => setIsDeleteModalOpen(true)}
              className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
            >
              Delete
            </button>
          </div>
        </div>

        {success && (
          <div className="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            {success}
          </div>
        )}

        <div className="bg-white rounded-lg shadow-md overflow-hidden mb-8">
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold text-gray-800">Appointment Information</h2>
              <div className="flex items-center">
                <span
                  className={`px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full ${getStatusBadgeClass(
                    appointment.status
                  )}`}
                >
                  {appointment.status}
                </span>
                <button
                  onClick={() => setIsStatusModalOpen(true)}
                  className="ml-4 text-blue-600 hover:text-blue-800"
                >
                  Change Status
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">Date & Time</h3>
                <p className="text-gray-600 mb-1">
                  <span className="font-medium">Date:</span> {formatDate(appointment.startTime)}
                </p>
                <p className="text-gray-600 mb-1">
                  <span className="font-medium">Time:</span> {formatTime(appointment.startTime)} - {formatTime(appointment.endTime)}
                </p>
                <p className="text-gray-600">
                  <span className="font-medium">Duration:</span> {appointment.service.duration} minutes
                </p>
              </div>

              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-2">Service</h3>
                <p className="text-gray-600 mb-1">
                  <span className="font-medium">Service:</span> {appointment.service.name}
                </p>
                <p className="text-gray-600 mb-1">
                  <span className="font-medium">Price:</span> {formatPrice(appointment.service.price)}
                </p>
                <p className="text-gray-600">
                  <span className="font-medium">Description:</span> {appointment.service.description || 'No description'}
                </p>
              </div>
            </div>

            <div className="border-t border-gray-200 mt-6 pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium text-gray-800 mb-2">Client Information</h3>
                  <p className="text-gray-600 mb-1">
                    <span className="font-medium">Name:</span> {appointment.client.firstName} {appointment.client.lastName}
                  </p>
                  <p className="text-gray-600 mb-1">
                    <span className="font-medium">Email:</span> {appointment.client.email}
                  </p>
                  <p className="text-gray-600">
                    <span className="font-medium">Phone:</span> {appointment.client.phone || 'Not provided'}
                  </p>
                </div>

                <div>
                  <h3 className="text-lg font-medium text-gray-800 mb-2">Staff Information</h3>
                  <p className="text-gray-600 mb-1">
                    <span className="font-medium">Name:</span> {appointment.staff.firstName} {appointment.staff.lastName}
                  </p>
                  <p className="text-gray-600 mb-1">
                    <span className="font-medium">Email:</span> {appointment.staff.email}
                  </p>
                  <p className="text-gray-600">
                    <span className="font-medium">Phone:</span> {appointment.staff.phone || 'Not provided'}
                  </p>
                </div>
              </div>
            </div>

            {appointment.notes && (
              <div className="border-t border-gray-200 mt-6 pt-6">
                <h3 className="text-lg font-medium text-gray-800 mb-2">Notes</h3>
                <p className="text-gray-600 whitespace-pre-line">{appointment.notes}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Cancel Appointment Modal */}
      <Modal
        isOpen={isCancelModalOpen}
        onClose={() => setIsCancelModalOpen(false)}
        title="Cancel Appointment"
        footer={
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => setIsCancelModalOpen(false)}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            >
              No, Keep It
            </button>
            <button
              onClick={handleCancelAppointment}
              className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700"
            >
              Yes, Cancel
            </button>
          </div>
        }
      >
        <p className="text-gray-700">
          Are you sure you want to cancel this appointment?
        </p>
        <p className="text-gray-500 mt-2">
          This will mark the appointment as cancelled but keep it in the system. The client and staff will be notified.
        </p>
      </Modal>

      {/* Delete Appointment Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete Appointment"
        footer={
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => setIsDeleteModalOpen(false)}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            >
              No, Keep It
            </button>
            <button
              onClick={handleDeleteAppointment}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Yes, Delete Permanently
            </button>
          </div>
        }
      >
        <p className="text-gray-700">
          Are you sure you want to permanently delete this appointment?
        </p>
        <p className="text-gray-500 mt-2">
          This action cannot be undone. The appointment will be completely removed from the system.
        </p>
      </Modal>

      {/* Change Status Modal */}
      <Modal
        isOpen={isStatusModalOpen}
        onClose={() => setIsStatusModalOpen(false)}
        title="Change Appointment Status"
        footer={
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => setIsStatusModalOpen(false)}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              onClick={handleStatusChange}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              disabled={!newStatus}
            >
              Update Status
            </button>
          </div>
        }
      >
        <p className="text-gray-700 mb-4">
          Select a new status for this appointment:
        </p>
        <select
          value={newStatus}
          onChange={(e) => setNewStatus(e.target.value as AppointmentStatus)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="">Select a status</option>
          <option value={AppointmentStatus.PENDING}>Pending</option>
          <option value={AppointmentStatus.CONFIRMED}>Confirmed</option>
          <option value={AppointmentStatus.COMPLETED}>Completed</option>
          <option value={AppointmentStatus.CANCELLED}>Cancelled</option>
          <option value={AppointmentStatus.NO_SHOW}>No Show</option>
        </select>
      </Modal>
    </Layout>
  );
};

export default AppointmentDetail;
