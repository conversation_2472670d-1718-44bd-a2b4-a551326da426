import { createClient } from '@supabase/supabase-js';
import config from '../config';
import { getUserSubscription } from './subscriptionService';
import { TierLevel, getTierById } from '../models/SubscriptionTier';
import { isSubscriptionActive } from '../models/Subscription';

// Initialize Supabase client
const supabase = createClient(config.supabase.url, config.supabase.serviceKey);

// Get staff count for a tenant
export async function getStaffCount(userId: string): Promise<number> {
  try {
    // Get all tenants owned by the user
    const { data: tenants, error: tenantError } = await supabase
      .from('tenants')
      .select('id')
      .eq('owner_id', userId);
    
    if (tenantError) {
      console.error('Error fetching tenants:', tenantError);
      return 0;
    }
    
    if (!tenants || tenants.length === 0) {
      return 0;
    }
    
    const tenantIds = tenants.map(tenant => tenant.id);
    
    // Get staff count for all user's tenants (excluding the owner)
    const { count, error: countError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .in('tenant_id', tenantIds)
      .eq('role', 'staff')
      .neq('id', userId);
    
    if (countError) {
      console.error('Error counting staff:', countError);
      return 0;
    }
    
    return count || 0;
  } catch (error) {
    console.error('Error in getStaffCount:', error);
    return 0;
  }
}

// Check if user can add more staff
export async function canAddStaff(userId: string): Promise<{
  canAdd: boolean;
  reason?: string;
  limit?: number;
  current?: number;
}> {
  try {
    // Get user's active subscription
    const subscription = await getUserSubscription(userId);
    
    // Check if subscription is active
    if (!subscription || !isSubscriptionActive(subscription)) {
      return {
        canAdd: false,
        reason: 'No active subscription found'
      };
    }
    
    // Get the tier details
    const userTier = subscription.tier_id;
    const tier = getTierById(userTier);
    
    if (!tier) {
      return {
        canAdd: false,
        reason: 'Invalid subscription tier'
      };
    }
    
    // If unlimited staff members, allow addition
    if (tier.maxStaffMembers === null) {
      return { canAdd: true };
    }
    
    // Check current staff count
    const count = await getStaffCount(userId);
    
    if (count >= tier.maxStaffMembers) {
      return {
        canAdd: false,
        reason: `You've reached your staff member limit (${tier.maxStaffMembers})`,
        limit: tier.maxStaffMembers,
        current: count
      };
    }
    
    // User is within limits
    return {
      canAdd: true,
      limit: tier.maxStaffMembers,
      current: count
    };
  } catch (error) {
    console.error('Error in canAddStaff:', error);
    return {
      canAdd: false,
      reason: 'Error checking staff limits'
    };
  }
}

// Get staff by ID
export async function getStaffById(staffId: string) {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', staffId)
      .eq('role', 'staff')
      .single();
    
    if (error) {
      console.error('Error fetching staff:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error in getStaffById:', error);
    return null;
  }
}

// Create a new staff member
export async function createStaff(staffData: any) {
  try {
    const { data, error } = await supabase
      .from('users')
      .insert(staffData)
      .select()
      .single();
    
    if (error) {
      console.error('Error creating staff:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error in createStaff:', error);
    return null;
  }
}

// Update a staff member
export async function updateStaff(staffId: string, updateData: any) {
  try {
    const { data, error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', staffId)
      .eq('role', 'staff')
      .select()
      .single();
    
    if (error) {
      console.error('Error updating staff:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error in updateStaff:', error);
    return null;
  }
}

// Delete a staff member
export async function deleteStaff(staffId: string) {
  try {
    const { error } = await supabase
      .from('users')
      .delete()
      .eq('id', staffId)
      .eq('role', 'staff');
    
    if (error) {
      console.error('Error deleting staff:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error in deleteStaff:', error);
    return false;
  }
}
