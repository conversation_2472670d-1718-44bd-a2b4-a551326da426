import cron from 'node-cron';
import {
  AppointmentStatus,
  NotificationType,
  NotificationChannel
} from '../models';
import {
  Appointment,
  User,
  Service,
  Tenant,
  Notification
} from './modelHelpers';
import { sendAppointmentReminder as sendLegacyReminderEmail } from './email';
import { sendAppointmentReminder as sendReminderEmail } from './resendEmail';
import { sendAppointmentReminderSMS as sendReminderSMS } from './sms';
import { Op } from './queryOperators';


/**
 * Send appointment reminders for appointments scheduled for tomorrow
 */
export const sendAppointmentReminders = async (): Promise<void> => {
  try {
    console.log('Running scheduled appointment reminders task...');

    // Get tomorrow's date
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    const tomorrowEnd = new Date(tomorrow);
    tomorrowEnd.setHours(23, 59, 59, 999);

    // Get all tenants
    const tenants = await Tenant.findAll({
      where: {
        active: true,
      },
    });

    // Process each tenant
    for (const tenant of tenants) {
      console.log(`Processing reminders for tenant: ${tenant.name} (${tenant.id})`);

      // Get appointments for tomorrow that haven't had reminders sent
      const appointments = await Appointment.findAll({
        where: {
          tenantId: tenant.id,
          startTime: {
            [Op.between]: [tomorrow, tomorrowEnd],
          },
          status: AppointmentStatus.CONFIRMED,
          reminderSent: false,
        },
        include: [
          {
            model: User,
            as: 'client',
          },
          {
            model: User,
            as: 'staff',
          },
          {
            model: Service,
            as: 'service',
          },
          {
            model: Tenant,
            as: 'tenant',
          },
        ],
      });

      console.log(`Found ${appointments.length} appointments that need reminders for tenant ${tenant.id}`);

      // Send reminders for each appointment
      for (const appointment of appointments) {
        try {
          // Use type assertion to access the associations
          const appointmentWithAssociations = appointment as any;

          // Send email reminder
          const emailSent = await sendReminderEmail(
            appointment,
            appointmentWithAssociations.client,
            appointmentWithAssociations.staff,
            appointmentWithAssociations.service,
            appointmentWithAssociations.tenant
          );

          // Create notification record for email
          await Notification.create({
            userId: appointmentWithAssociations.client.id,
            appointmentId: appointment.id,
            type: NotificationType.APPOINTMENT_REMINDER,
            channel: NotificationChannel.EMAIL,
            content: `Reminder for your appointment for ${appointmentWithAssociations.service.name} with ${appointmentWithAssociations.staff.firstName} ${appointmentWithAssociations.staff.lastName} tomorrow at ${appointment.startTime.toLocaleTimeString()}`,
            sent: emailSent,
            sentAt: emailSent ? new Date() : undefined,
            error: emailSent ? undefined : 'Failed to send email reminder',
          });

          // Send SMS reminder if client has phone
          let smsSent = false;
          if (appointmentWithAssociations.client.phone) {
            smsSent = await sendReminderSMS(
              appointment,
              appointmentWithAssociations.client,
              appointmentWithAssociations.staff,
              appointmentWithAssociations.service,
              appointmentWithAssociations.tenant
            );

            // Create notification record for SMS
            await Notification.create({
              userId: appointmentWithAssociations.client.id,
              appointmentId: appointment.id,
              type: NotificationType.APPOINTMENT_REMINDER,
              channel: NotificationChannel.SMS,
              content: `Reminder for your appointment for ${appointmentWithAssociations.service.name} with ${appointmentWithAssociations.staff.firstName} ${appointmentWithAssociations.staff.lastName} tomorrow at ${appointment.startTime.toLocaleTimeString()}`,
              sent: smsSent,
              sentAt: smsSent ? new Date() : undefined,
              error: smsSent ? undefined : 'Failed to send SMS reminder',
            });
          }

          // Mark appointment as having had reminders sent
          await Appointment.update(
            { reminderSent: true },
            {
              where: {
                id: appointment.id
              }
            }
          );

          console.log(`Sent reminders for appointment ${appointment.id} - Email: ${emailSent}, SMS: ${smsSent}`);
        } catch (error) {
          console.error(`Failed to send reminder for appointment ${appointment.id}:`, error);
        }
      }
    }

    console.log('Appointment reminders task completed');
  } catch (error) {
    console.error('Error in appointment reminders task:', error);
  }
};

/**
 * Initialize the scheduler
 */
export const initializeScheduler = (): void => {
  // Schedule appointment reminders to run daily at 9:00 AM
  cron.schedule('0 9 * * *', async () => {
    console.log('Running scheduled task: Send appointment reminders');
    await sendAppointmentReminders();
  });

  // Add more scheduled tasks here as needed

  console.log('Scheduler initialized');
};
