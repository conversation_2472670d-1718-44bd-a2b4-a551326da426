import { Request, Response, NextFunction, RequestHandler } from 'express';

/**
 * Wraps a controller function to make it compatible with Express route handlers
 * This solves TypeScript errors related to return types
 */
export const asyncHandler = (
  fn: (req: Request, res: Response, next?: NextFunction) => Promise<any>
): RequestHandler => {
  return (req: Request, res: Response, next: NextFunction): void => {
    Promise.resolve(fn(req, res, next))
      .then(() => {
        // Don't call next() if the response has been sent
        // The controller should handle the response completely
      })
      .catch((error) => {
        next(error);
      });
  };
};

/**
 * Wraps middleware to ensure it returns void and properly calls next()
 */
export const wrapMiddleware = (
  middleware: (req: Request, res: Response, next: NextFunction) => Promise<any> | any
): RequestHandler => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const result = middleware(req, res, next);

      // If it's a promise, handle it
      if (result && typeof result.then === 'function') {
        result
          .then(() => {
            // If the response hasn't been sent and next hasn't been called, call next
            if (!res.headersSent) {
              next();
            }
          })
          .catch((error: any) => {
            next(error);
          });
      }
      // If it's not a promise and next hasn't been called, call next
      else if (!res.headersSent) {
        // Only call next if it wasn't already called in the middleware
        next();
      }
    } catch (error) {
      next(error);
    }
  };
};
