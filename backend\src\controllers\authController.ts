import { Request, Response } from 'express';
import { UserRole, User, Tenant, UserDB, TenantDB } from '../models';
import { generateToken } from '../utils/jwt';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import { supabase } from '../config/supabase';
import { toCamelCase, toSnakeCase } from '../utils/modelHelpers';

// Define a type that includes the tenant association
interface UserWithTenant extends UserDB {
  tenant?: TenantDB;
}

// Define a type for the response user object
interface UserResponse {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: UserRole;
  tenantId?: string;
  phone?: string;
  createdAt?: Date;
}

// Define a type for the response tenant object
interface TenantResponse {
  id: string;
  name: string;
  logo?: string;
  primaryColor?: string;
  secondaryColor?: string;
}

// Register a new tenant (business)
export const registerTenant = async (req: Request, res: Response) => {
  try {
    const {
      name,
      email,
      password,
      phone,
      address,
      firstName,
      lastName
    } = req.body;

    // Check if tenant with email already exists
    const { data: existingTenant } = await supabase
      .from('tenants')
      .select('*')
      .eq('email', email)
      .single();

    if (existingTenant) {
      return res.status(400).json({ message: 'Business with this email already exists' });
    }

    // Create new tenant
    const tenantId = uuidv4();
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .insert({
        id: tenantId,
        name,
        email,
        phone,
        address,
        active: true,
      })
      .select()
      .single();

    if (tenantError) {
      console.error('Tenant creation error:', tenantError);
      return res.status(500).json({ message: 'Failed to create business' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create admin user for the tenant
    const userId = uuidv4();
    const { data: adminUser, error: userError } = await supabase
      .from('users')
      .insert({
        id: userId,
        tenant_id: tenant.id,
        first_name: firstName,
        last_name: lastName,
        email,
        password: hashedPassword,
        phone,
        role: UserRole.ADMIN,
        active: true,
      })
      .select()
      .single();

    if (userError) {
      console.error('User creation error:', userError);
      return res.status(500).json({ message: 'Failed to create admin user' });
    }

    // Generate JWT token
    const token = generateToken(adminUser);

    // Convert snake_case to camelCase for response
    const userResponse: UserResponse = {
      id: adminUser.id,
      firstName: adminUser.first_name,
      lastName: adminUser.last_name,
      email: adminUser.email,
      role: adminUser.role,
    };

    const tenantResponse: TenantResponse = {
      id: tenant.id,
      name: tenant.name,
    };

    res.status(201).json({
      message: 'Tenant registered successfully',
      token,
      user: userResponse,
      tenant: tenantResponse,
    });
  } catch (error) {
    console.error('Tenant registration error:', error);
    res.status(500).json({ message: 'Failed to register tenant' });
  }
};

// Login user
export const login = async (req: Request, res: Response) => {
  try {
    console.log('Login attempt:', req.body.email);
    const { email, password, tenantId } = req.body;

    // Find user by email and tenant ID
    let query = supabase
      .from('users')
      .select(`
        *,
        tenant:tenants(*)
      `)
      .eq('email', email);

    if (tenantId) {
      query = query.eq('tenant_id', tenantId);
    }

    const { data: user, error } = await query.single();

    if (error) {
      console.log('User query error:', error);
    }

    const userWithTenant = user as unknown as UserWithTenant;

    if (!userWithTenant) {
      console.log('User not found:', email);
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Check if user is active
    if (!userWithTenant.active) {
      console.log('User is inactive:', email);
      return res.status(401).json({ message: 'Account is inactive' });
    }

    // Check if tenant is active
    if (userWithTenant.tenant && !userWithTenant.tenant.active) {
      console.log('Tenant is inactive for user:', email);
      return res.status(401).json({ message: 'Business account is inactive' });
    }

    // Check password
    if (!userWithTenant.password) {
      console.log('User has no password:', email);
      return res.status(401).json({ message: 'Invalid login method' });
    }

    console.log('Checking password for user:', email);

    try {
      // Use bcrypt directly for more reliable password comparison
      const isPasswordValid = await bcrypt.compare(password, userWithTenant.password);

      if (!isPasswordValid) {
        console.log('Invalid password for user:', email);
        return res.status(401).json({ message: 'Invalid credentials' });
      }
    } catch (error) {
      console.error('Password comparison error:', error);
      return res.status(500).json({ message: 'Login failed' });
    }

    // Generate JWT token
    const token = generateToken({
      id: userWithTenant.id,
      email: userWithTenant.email,
      tenantId: userWithTenant.tenant_id,
      role: userWithTenant.role
    });

    console.log('Login successful for user:', email);

    // Convert snake_case to camelCase for response
    const userResponse: UserResponse = {
      id: userWithTenant.id,
      firstName: userWithTenant.first_name,
      lastName: userWithTenant.last_name,
      email: userWithTenant.email,
      role: userWithTenant.role,
      tenantId: userWithTenant.tenant_id,
      phone: userWithTenant.phone
    };

    const tenantResponse: TenantResponse | null = userWithTenant.tenant ? {
      id: userWithTenant.tenant.id,
      name: userWithTenant.tenant.name,
      logo: userWithTenant.tenant.logo,
      primaryColor: userWithTenant.tenant.primary_color,
      secondaryColor: userWithTenant.tenant.secondary_color
    } : null;

    res.json({
      message: 'Login successful',
      token,
      user: userResponse,
      tenant: tenantResponse,
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Login failed' });
  }
};

// Register a new client
export const registerClient = async (req: Request, res: Response) => {
  try {
    const {
      firstName,
      lastName,
      email,
      password,
      phone,
      tenantId
    } = req.body;

    // Check if tenant exists
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .select('*')
      .eq('id', tenantId)
      .single();

    if (tenantError || !tenant) {
      return res.status(404).json({ message: 'Business not found' });
    }

    // Check if client with email already exists for this tenant
    const { data: existingClient, error: clientError } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .eq('tenant_id', tenantId)
      .single();

    if (existingClient) {
      return res.status(400).json({ message: 'Client with this email already exists' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new client
    const userId = uuidv4();
    const { data: client, error: createError } = await supabase
      .from('users')
      .insert({
        id: userId,
        tenant_id: tenantId,
        first_name: firstName,
        last_name: lastName,
        email,
        password: hashedPassword,
        phone,
        role: UserRole.CLIENT,
        active: true,
      })
      .select()
      .single();

    if (createError) {
      console.error('Client creation error:', createError);
      return res.status(500).json({ message: 'Failed to create client' });
    }

    // Generate JWT token
    const token = generateToken({
      id: client.id,
      email: client.email,
      tenantId: client.tenant_id,
      role: client.role
    });

    // Convert snake_case to camelCase for response
    const userResponse: UserResponse = {
      id: client.id,
      firstName: client.first_name,
      lastName: client.last_name,
      email: client.email,
      role: client.role,
      tenantId: client.tenant_id,
      phone: client.phone
    };

    res.status(201).json({
      message: 'Client registered successfully',
      token,
      user: userResponse,
    });
  } catch (error) {
    console.error('Client registration error:', error);
    res.status(500).json({ message: 'Failed to register client' });
  }
};

// Get current user profile
export const getProfile = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { data: user, error } = await supabase
      .from('users')
      .select(`
        *,
        tenant:tenants(*)
      `)
      .eq('id', req.user.id)
      .single();

    if (error || !user) {
      return res.status(404).json({ message: 'User not found' });
    }

    const userWithTenant = user as unknown as UserWithTenant;

    // Convert snake_case to camelCase for response
    const userResponse: UserResponse = {
      id: userWithTenant.id,
      firstName: userWithTenant.first_name,
      lastName: userWithTenant.last_name,
      email: userWithTenant.email,
      phone: userWithTenant.phone,
      role: userWithTenant.role,
      tenantId: userWithTenant.tenant_id,
      createdAt: new Date(userWithTenant.created_at)
    };

    const tenantResponse: TenantResponse | null = userWithTenant.tenant ? {
      id: userWithTenant.tenant.id,
      name: userWithTenant.tenant.name,
      logo: userWithTenant.tenant.logo,
      primaryColor: userWithTenant.tenant.primary_color,
      secondaryColor: userWithTenant.tenant.secondary_color
    } : null;

    res.json({
      user: userResponse,
      tenant: tenantResponse
    });
  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({ message: 'Failed to get profile' });
  }
};

// Update user password
export const updatePassword = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { currentPassword, newPassword } = req.body;

    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', req.user.id)
      .single();

    if (error || !user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Check current password
    if (!user.password) {
      return res.status(400).json({ message: 'Cannot update password for social login' });
    }

    const isPasswordValid = await bcrypt.compare(currentPassword, user.password);

    if (!isPasswordValid) {
      return res.status(401).json({ message: 'Current password is incorrect' });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password
    const { error: updateError } = await supabase
      .from('users')
      .update({ password: hashedPassword })
      .eq('id', req.user.id);

    if (updateError) {
      console.error('Password update error:', updateError);
      return res.status(500).json({ message: 'Failed to update password' });
    }

    res.json({ message: 'Password updated successfully' });
  } catch (error) {
    console.error('Update password error:', error);
    res.status(500).json({ message: 'Failed to update password' });
  }
};
