import { createClient } from '@supabase/supabase-js';
import config from '../config';
import { userHasFeature } from './subscriptionService';

// Initialize Supabase client
const supabase = createClient(config.supabase.url, config.supabase.serviceKey);

// Check if user can use analytics and reporting
export async function canUseAnalytics(userId: string): Promise<{
  canUse: boolean;
  reason?: string;
}> {
  try {
    const hasFeature = await userHasFeature(userId, 'analyticsAndReporting');
    
    if (!hasFeature) {
      return {
        canUse: false,
        reason: 'Analytics and reporting requires a Pro or Pro+ subscription'
      };
    }
    
    return { canUse: true };
  } catch (error) {
    console.error('Error in canUseAnalytics:', error);
    return {
      canUse: false,
      reason: 'Error checking feature access'
    };
  }
}

// Get appointment statistics
export async function getAppointmentStats(tenantId: string, userId: string, period: string) {
  try {
    // Check if user can use analytics
    const canUse = await canUseAnalytics(userId);
    
    if (!canUse.canUse) {
      return {
        success: false,
        message: canUse.reason,
        upgradeRequired: true,
        requiredFeature: 'analyticsAndReporting'
      };
    }
    
    // Calculate date range based on period
    const now = new Date();
    let startDate: Date;
    
    switch (period) {
      case 'week':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        startDate = new Date(now);
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 1);
    }
    
    // Get total appointments
    const { count: totalCount, error: totalError } = await supabase
      .from('appointments')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenantId)
      .gte('created_at', startDate.toISOString());
    
    if (totalError) {
      console.error('Error counting appointments:', totalError);
      return {
        success: false,
        message: 'Failed to fetch appointment statistics'
      };
    }
    
    // Get appointments by status
    const { data: byStatus, error: statusError } = await supabase
      .from('appointments')
      .select('status, count')
      .eq('tenant_id', tenantId)
      .gte('created_at', startDate.toISOString())
      .group('status');
    
    if (statusError) {
      console.error('Error counting appointments by status:', statusError);
      return {
        success: false,
        message: 'Failed to fetch appointment statistics'
      };
    }
    
    // Get appointments by service
    const { data: byService, error: serviceError } = await supabase
      .from('appointments')
      .select('service_id, services(name), count')
      .eq('tenant_id', tenantId)
      .gte('created_at', startDate.toISOString())
      .group('service_id, services(name)');
    
    if (serviceError) {
      console.error('Error counting appointments by service:', serviceError);
      return {
        success: false,
        message: 'Failed to fetch appointment statistics'
      };
    }
    
    // Get appointments by staff
    const { data: byStaff, error: staffError } = await supabase
      .from('appointments')
      .select('staff_id, users!staff_id(first_name, last_name), count')
      .eq('tenant_id', tenantId)
      .gte('created_at', startDate.toISOString())
      .group('staff_id, users!staff_id(first_name, last_name)');
    
    if (staffError) {
      console.error('Error counting appointments by staff:', staffError);
      return {
        success: false,
        message: 'Failed to fetch appointment statistics'
      };
    }
    
    // Get appointments by day
    const { data: byDay, error: dayError } = await supabase
      .from('appointments')
      .select('created_at::date as date, count')
      .eq('tenant_id', tenantId)
      .gte('created_at', startDate.toISOString())
      .group('date')
      .order('date', { ascending: true });
    
    if (dayError) {
      console.error('Error counting appointments by day:', dayError);
      return {
        success: false,
        message: 'Failed to fetch appointment statistics'
      };
    }
    
    return {
      success: true,
      data: {
        total: totalCount,
        byStatus,
        byService,
        byStaff,
        byDay
      }
    };
  } catch (error) {
    console.error('Error in getAppointmentStats:', error);
    return {
      success: false,
      message: 'Internal server error'
    };
  }
}

// Get client statistics
export async function getClientStats(tenantId: string, userId: string, period: string) {
  try {
    // Check if user can use analytics
    const canUse = await canUseAnalytics(userId);
    
    if (!canUse.canUse) {
      return {
        success: false,
        message: canUse.reason,
        upgradeRequired: true,
        requiredFeature: 'analyticsAndReporting'
      };
    }
    
    // Calculate date range based on period
    const now = new Date();
    let startDate: Date;
    
    switch (period) {
      case 'week':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        startDate = new Date(now);
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 1);
    }
    
    // Get total clients
    const { count: totalCount, error: totalError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenantId)
      .eq('role', 'client');
    
    if (totalError) {
      console.error('Error counting clients:', totalError);
      return {
        success: false,
        message: 'Failed to fetch client statistics'
      };
    }
    
    // Get new clients in period
    const { count: newCount, error: newError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenantId)
      .eq('role', 'client')
      .gte('created_at', startDate.toISOString());
    
    if (newError) {
      console.error('Error counting new clients:', newError);
      return {
        success: false,
        message: 'Failed to fetch client statistics'
      };
    }
    
    // Get clients by day
    const { data: byDay, error: dayError } = await supabase
      .from('users')
      .select('created_at::date as date, count')
      .eq('tenant_id', tenantId)
      .eq('role', 'client')
      .gte('created_at', startDate.toISOString())
      .group('date')
      .order('date', { ascending: true });
    
    if (dayError) {
      console.error('Error counting clients by day:', dayError);
      return {
        success: false,
        message: 'Failed to fetch client statistics'
      };
    }
    
    return {
      success: true,
      data: {
        total: totalCount,
        new: newCount,
        byDay
      }
    };
  } catch (error) {
    console.error('Error in getClientStats:', error);
    return {
      success: false,
      message: 'Internal server error'
    };
  }
}
