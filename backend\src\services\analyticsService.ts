import { createClient } from '@supabase/supabase-js';
import config from '../config';
import { userHasFeature } from './subscriptionService';

// Initialize Supabase client
const supabase = createClient(config.supabase.url, config.supabase.serviceKey);

// Check if user can use analytics and reporting
export async function canUseAnalytics(userId: string): Promise<{
  canUse: boolean;
  reason?: string;
}> {
  try {
    const hasFeature = await userHasFeature(userId, 'analyticsAndReporting');
    
    if (!hasFeature) {
      return {
        canUse: false,
        reason: 'Analytics and reporting requires a Pro or Pro+ subscription'
      };
    }
    
    return { canUse: true };
  } catch (error) {
    console.error('Error in canUseAnalytics:', error);
    return {
      canUse: false,
      reason: 'Error checking feature access'
    };
  }
}

// Get appointment statistics
export async function getAppointmentStats(tenantId: string, userId: string, period: string) {
  try {
    // Check if user can use analytics
    const canUse = await canUseAnalytics(userId);
    
    if (!canUse.canUse) {
      return {
        success: false,
        message: canUse.reason,
        upgradeRequired: true,
        requiredFeature: 'analyticsAndReporting'
      };
    }
    
    // Calculate date range based on period
    const now = new Date();
    let startDate: Date;
    
    switch (period) {
      case 'week':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        startDate = new Date(now);
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 1);
    }
    
    // Get total appointments
    const { count: totalCount, error: totalError } = await supabase
      .from('appointments')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenantId)
      .gte('created_at', startDate.toISOString());
    
    if (totalError) {
      console.error('Error counting appointments:', totalError);
      return {
        success: false,
        message: 'Failed to fetch appointment statistics'
      };
    }
    
    // Get appointments by status (simplified approach)
    const { data: allAppointments, error: statusError } = await supabase
      .from('appointments')
      .select('status')
      .eq('tenant_id', tenantId)
      .gte('created_at', startDate.toISOString());

    // Group by status manually
    const byStatus = allAppointments?.reduce((acc: any[], appointment: any) => {
      const existing = acc.find(item => item.status === appointment.status);
      if (existing) {
        existing.count++;
      } else {
        acc.push({ status: appointment.status, count: 1 });
      }
      return acc;
    }, []) || [];
    
    if (statusError) {
      console.error('Error counting appointments by status:', statusError);
      return {
        success: false,
        message: 'Failed to fetch appointment statistics'
      };
    }
    
    // Get appointments by service (simplified approach)
    const { data: serviceAppointments, error: serviceError } = await supabase
      .from('appointments')
      .select('service_id, services(name)')
      .eq('tenant_id', tenantId)
      .gte('created_at', startDate.toISOString());

    // Group by service manually
    const byService = serviceAppointments?.reduce((acc: any[], appointment: any) => {
      const existing = acc.find(item => item.service_id === appointment.service_id);
      if (existing) {
        existing.count++;
      } else {
        acc.push({
          service_id: appointment.service_id,
          services: appointment.services,
          count: 1
        });
      }
      return acc;
    }, []) || [];
    
    if (serviceError) {
      console.error('Error counting appointments by service:', serviceError);
      return {
        success: false,
        message: 'Failed to fetch appointment statistics'
      };
    }
    
    // Get appointments by staff (simplified approach)
    const { data: staffAppointments, error: staffError } = await supabase
      .from('appointments')
      .select('staff_id, users!staff_id(first_name, last_name)')
      .eq('tenant_id', tenantId)
      .gte('created_at', startDate.toISOString());

    // Group by staff manually
    const byStaff = staffAppointments?.reduce((acc: any[], appointment: any) => {
      const existing = acc.find(item => item.staff_id === appointment.staff_id);
      if (existing) {
        existing.count++;
      } else {
        acc.push({
          staff_id: appointment.staff_id,
          users: appointment.users,
          count: 1
        });
      }
      return acc;
    }, []) || [];
    
    if (staffError) {
      console.error('Error counting appointments by staff:', staffError);
      return {
        success: false,
        message: 'Failed to fetch appointment statistics'
      };
    }
    
    // Get appointments by day (simplified approach)
    const { data: dayAppointments, error: dayError } = await supabase
      .from('appointments')
      .select('created_at')
      .eq('tenant_id', tenantId)
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });

    // Group by day manually
    const byDay = dayAppointments?.reduce((acc: any[], appointment: any) => {
      const date = new Date(appointment.created_at).toISOString().split('T')[0];
      const existing = acc.find(item => item.date === date);
      if (existing) {
        existing.count++;
      } else {
        acc.push({ date, count: 1 });
      }
      return acc;
    }, []) || [];
    
    if (dayError) {
      console.error('Error counting appointments by day:', dayError);
      return {
        success: false,
        message: 'Failed to fetch appointment statistics'
      };
    }
    
    return {
      success: true,
      data: {
        total: totalCount,
        byStatus,
        byService,
        byStaff,
        byDay
      }
    };
  } catch (error) {
    console.error('Error in getAppointmentStats:', error);
    return {
      success: false,
      message: 'Internal server error'
    };
  }
}

// Get client statistics
export async function getClientStats(tenantId: string, userId: string, period: string) {
  try {
    // Check if user can use analytics
    const canUse = await canUseAnalytics(userId);
    
    if (!canUse.canUse) {
      return {
        success: false,
        message: canUse.reason,
        upgradeRequired: true,
        requiredFeature: 'analyticsAndReporting'
      };
    }
    
    // Calculate date range based on period
    const now = new Date();
    let startDate: Date;
    
    switch (period) {
      case 'week':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        startDate = new Date(now);
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 1);
    }
    
    // Get total clients
    const { count: totalCount, error: totalError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenantId)
      .eq('role', 'client');
    
    if (totalError) {
      console.error('Error counting clients:', totalError);
      return {
        success: false,
        message: 'Failed to fetch client statistics'
      };
    }
    
    // Get new clients in period
    const { count: newCount, error: newError } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .eq('tenant_id', tenantId)
      .eq('role', 'client')
      .gte('created_at', startDate.toISOString());
    
    if (newError) {
      console.error('Error counting new clients:', newError);
      return {
        success: false,
        message: 'Failed to fetch client statistics'
      };
    }
    
    // Get clients by day (simplified approach)
    const { data: dayClients, error: dayError } = await supabase
      .from('users')
      .select('created_at')
      .eq('tenant_id', tenantId)
      .eq('role', 'client')
      .gte('created_at', startDate.toISOString())
      .order('created_at', { ascending: true });

    // Group by day manually
    const byDay = dayClients?.reduce((acc: any[], client: any) => {
      const date = new Date(client.created_at).toISOString().split('T')[0];
      const existing = acc.find(item => item.date === date);
      if (existing) {
        existing.count++;
      } else {
        acc.push({ date, count: 1 });
      }
      return acc;
    }, []) || [];
    
    if (dayError) {
      console.error('Error counting clients by day:', dayError);
      return {
        success: false,
        message: 'Failed to fetch client statistics'
      };
    }
    
    return {
      success: true,
      data: {
        total: totalCount,
        new: newCount,
        byDay
      }
    };
  } catch (error) {
    console.error('Error in getClientStats:', error);
    return {
      success: false,
      message: 'Internal server error'
    };
  }
}
