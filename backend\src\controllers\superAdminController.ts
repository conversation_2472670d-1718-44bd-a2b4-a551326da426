import { Request, Response } from 'express';
import { UserRole, Tenant as TenantType } from '../models/supabase';
import { supabase } from '../config/supabase';

// Get all tenants (businesses)
export const getAllTenants = async (req: Request, res: Response) => {
  try {
    console.log('Fetching all tenants...');

    const { data: tenants, error } = await supabase
      .from('tenants')
      .select('id, name, email, phone, address, active, created_at')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching tenants:', error);
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch tenants',
        error: error.message
      });
    }

    console.log(`Successfully fetched ${tenants?.length || 0} tenants`);

    // Convert snake_case to camelCase for frontend
    const formattedTenants = tenants?.map(tenant => ({
      id: tenant.id,
      name: tenant.name,
      email: tenant.email,
      phone: tenant.phone,
      address: tenant.address,
      active: tenant.active,
      createdAt: tenant.created_at
    })) || [];

    return res.status(200).json({
      success: true,
      data: formattedTenants
    });
  } catch (error) {
    console.error('Error fetching tenants:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch tenants',
      error: error instanceof Error ? error.message : String(error)
    });
  }
};

// Get detailed tenant information including stats
export const getTenantDetails = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;

    // Get tenant details
    const { data: tenant, error: tenantError } = await supabase
      .from('tenants')
      .select('*')
      .eq('id', tenantId)
      .single();

    if (tenantError || !tenant) {
      console.error('Error fetching tenant:', tenantError);
      return res.status(404).json({
        success: false,
        message: 'Tenant not found'
      });
    }

    // Get tenant stats in parallel
    const [
      { count: userCount, error: userError },
      { count: appointmentCount, error: appointmentError },
      { count: serviceCount, error: serviceError }
    ] = await Promise.all([
      supabase.from('users').select('*', { count: 'exact', head: true }).eq('tenant_id', tenantId),
      supabase.from('appointments').select('*', { count: 'exact', head: true }).eq('tenant_id', tenantId),
      supabase.from('services').select('*', { count: 'exact', head: true }).eq('tenant_id', tenantId)
    ]);

    if (userError || appointmentError || serviceError) {
      console.error('Error fetching tenant stats:', { userError, appointmentError, serviceError });
    }

    // Convert snake_case to camelCase for frontend
    const formattedTenant = {
      id: tenant.id,
      name: tenant.name,
      email: tenant.email,
      phone: tenant.phone,
      address: tenant.address,
      logo: tenant.logo,
      primaryColor: tenant.primary_color,
      secondaryColor: tenant.secondary_color,
      welcomeMessage: tenant.welcome_message,
      currency: tenant.currency,
      timezone: tenant.timezone,
      active: tenant.active,
      createdAt: tenant.created_at,
      updatedAt: tenant.updated_at
    };

    return res.status(200).json({
      success: true,
      data: {
        tenant: formattedTenant,
        stats: {
          users: userCount || 0,
          appointments: appointmentCount || 0,
          services: serviceCount || 0
        }
      }
    });
  } catch (error) {
    console.error('Error fetching tenant details:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch tenant details'
    });
  }
};

// Update tenant status (activate/deactivate)
export const updateTenantStatus = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;
    const { active } = req.body;

    // Check if tenant exists first
    const { data: existingTenant, error: findError } = await supabase
      .from('tenants')
      .select('id')
      .eq('id', tenantId)
      .single();

    if (findError || !existingTenant) {
      return res.status(404).json({
        success: false,
        message: 'Tenant not found'
      });
    }

    // Update tenant status
    const { data: updatedTenant, error: updateError } = await supabase
      .from('tenants')
      .update({ active })
      .eq('id', tenantId)
      .select()
      .single();

    if (updateError) {
      console.error('Error updating tenant status:', updateError);
      return res.status(500).json({
        success: false,
        message: 'Failed to update tenant status'
      });
    }

    // Format response
    const formattedTenant = {
      id: updatedTenant.id,
      name: updatedTenant.name,
      email: updatedTenant.email,
      phone: updatedTenant.phone,
      address: updatedTenant.address,
      active: updatedTenant.active,
      createdAt: updatedTenant.created_at,
      updatedAt: updatedTenant.updated_at
    };

    return res.status(200).json({
      success: true,
      message: `Tenant ${active ? 'activated' : 'deactivated'} successfully`,
      data: formattedTenant
    });
  } catch (error) {
    console.error('Error updating tenant status:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update tenant status'
    });
  }
};

// Delete a tenant
export const deleteTenant = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;

    // Check if tenant exists first
    const { data: existingTenant, error: findError } = await supabase
      .from('tenants')
      .select('id')
      .eq('id', tenantId)
      .single();

    if (findError || !existingTenant) {
      return res.status(404).json({
        success: false,
        message: 'Tenant not found'
      });
    }

    // Delete the tenant
    const { error: deleteError } = await supabase
      .from('tenants')
      .delete()
      .eq('id', tenantId);

    if (deleteError) {
      console.error('Error deleting tenant:', deleteError);
      return res.status(500).json({
        success: false,
        message: 'Failed to delete tenant'
      });
    }

    return res.status(200).json({
      success: true,
      message: 'Tenant deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting tenant:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete tenant'
    });
  }
};

// Get super admin dashboard data
export const getSuperAdminDashboard = async (req: Request, res: Response) => {
  try {
    console.log('Fetching super admin dashboard data...');

    // Fetch all data in parallel for better performance
    const [
      { data: allTenants, error: tenantsError },
      { count: totalUsers, error: usersError },
      { count: totalAppointments, error: appointmentsError },
      { data: recentTenants, error: recentError }
    ] = await Promise.all([
      // Get all tenants for counting total and active
      supabase.from('tenants').select('id, active'),
      // Get total users count
      supabase.from('users').select('*', { count: 'exact', head: true }),
      // Get total appointments count
      supabase.from('appointments').select('*', { count: 'exact', head: true }),
      // Get recent tenants with full details
      supabase
        .from('tenants')
        .select('id, name, email, created_at, active')
        .order('created_at', { ascending: false })
        .limit(5)
    ]);

    if (tenantsError || usersError || appointmentsError || recentError) {
      console.error('Error fetching dashboard data:', {
        tenantsError,
        usersError,
        appointmentsError,
        recentError
      });
      return res.status(500).json({
        success: false,
        message: 'Failed to fetch dashboard data'
      });
    }

    // Calculate statistics
    const totalTenants = allTenants?.length || 0;
    const activeTenants = allTenants?.filter(tenant => tenant.active === true).length || 0;

    // Format recent tenants for frontend
    const formattedRecentTenants = recentTenants?.map(tenant => ({
      id: tenant.id,
      name: tenant.name,
      email: tenant.email,
      createdAt: tenant.created_at,
      active: tenant.active
    })) || [];

    console.log(`Dashboard Stats:
      Total Tenants: ${totalTenants}
      Active Tenants: ${activeTenants}
      Total Users: ${totalUsers || 0}
      Total Appointments: ${totalAppointments || 0}
      Recent Tenants: ${formattedRecentTenants.length}
    `);

    return res.status(200).json({
      success: true,
      data: {
        stats: {
          totalTenants,
          activeTenants,
          totalUsers: totalUsers || 0,
          totalAppointments: totalAppointments || 0
        },
        recentTenants: formattedRecentTenants
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard data',
      error: error instanceof Error ? error.message : String(error)
    });
  }
};