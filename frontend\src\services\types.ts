// Notification types
export enum NotificationType {
  APPOINTMENT_CREATED = 'appointment_created',
  APPOINTMENT_UPDATED = 'appointment_updated',
  APPOINTMENT_CANCELLED = 'appointment_cancelled',
  APPOINTMENT_REMINDER = 'appointment_reminder',
  CLIENT_ADDED = 'client_added',
  SERVICE_ADDED = 'service_added',
  STAFF_ADDED = 'staff_added',
}

// Notification channels
export enum NotificationChannel {
  EMAIL = 'email',
  SMS = 'sms',
  BOTH = 'both',
  SYSTEM = 'system',
}

// Activity type for the activity feed
export interface Activity {
  id: string;
  type: NotificationType | string;
  title: string;
  description: string;
  timestamp: string;
  relatedId?: string;
  relatedType?: 'appointment' | 'client' | 'service' | 'staff';
  icon?: string;
  iconColor?: string;
}
