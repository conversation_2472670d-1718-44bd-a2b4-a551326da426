# Setting Up Supabase Authentication for Scheduly

This guide explains how to set up and configure Supabase authentication for the Scheduly application.

## 1. Configure Supabase Auth Settings

1. Go to your Supabase project dashboard
2. Navigate to Authentication > Settings
3. Configure the following settings:

### Site URL
- Set the Site URL to your Vercel deployment URL (e.g., `https://scheduly.vercel.app`)
- This is used for redirects after authentication

### Email Auth
- Enable Email auth
- Configure the email templates for:
  - Confirmation
  - Invitation
  - Magic Link
  - Reset Password
- Customize the templates with your branding

### Redirect URLs
- Add your application URLs to the redirect whitelist:
  - `https://scheduly.vercel.app/login`
  - `https://scheduly.vercel.app/register`
  - `https://scheduly.vercel.app/reset-password`
  - `https://scheduly.vercel.app/google-calendar-callback`

### JWT Settings
- Set a custom JWT expiry time (e.g., 3600 seconds = 1 hour)

## 2. Set Up Email Provider (Optional)

For production, you should set up a custom SMTP server:

1. Go to Authentication > Email Templates
2. Click "Email Settings"
3. Select "Custom SMTP"
4. Enter your SMTP credentials:
   - Host (e.g., `smtp.gmail.com`)
   - Port (e.g., `587`)
   - Username (your email)
   - Password (your app password)
   - Sender Name (e.g., "Scheduly")

## 3. Implement Authentication in Frontend

### Install Supabase Auth Helpers

```bash
npm install @supabase/auth-helpers-react
```

### Set Up Auth Provider

In your `main.tsx` file:

```tsx
import { createClient } from '@supabase/supabase-js'
import { SessionContextProvider } from '@supabase/auth-helpers-react'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

const supabaseClient = createClient(supabaseUrl, supabaseAnonKey)

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <SessionContextProvider supabaseClient={supabaseClient}>
      <App />
    </SessionContextProvider>
  </React.StrictMode>,
)
```

### Create Auth Hooks

Create a file `src/hooks/useAuth.ts`:

```tsx
import { useSupabaseClient, useSession } from '@supabase/auth-helpers-react'
import { useState } from 'react'

export function useAuth() {
  const supabase = useSupabaseClient()
  const session = useSession()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true)
      setError(null)
      
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })
      
      if (error) throw error
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const signUp = async (email: string, password: string) => {
    try {
      setLoading(true)
      setError(null)
      
      const { error } = await supabase.auth.signUp({
        email,
        password,
      })
      
      if (error) throw error
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const { error } = await supabase.auth.signOut()
      
      if (error) throw error
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return {
    signIn,
    signUp,
    signOut,
    user: session?.user || null,
    isAuthenticated: !!session,
    loading,
    error,
  }
}
```

### Update Login Component

```tsx
import { useAuth } from '../hooks/useAuth'

function Login() {
  const { signIn, loading, error } = useAuth()
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    await signIn(email, password)
  }

  return (
    <form onSubmit={handleSubmit}>
      {error && <div className="error">{error}</div>}
      <input
        type="email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
        placeholder="Email"
        required
      />
      <input
        type="password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
        placeholder="Password"
        required
      />
      <button type="submit" disabled={loading}>
        {loading ? 'Logging in...' : 'Log In'}
      </button>
    </form>
  )
}
```

## 4. Sync Supabase Auth with Your Database

Create a Supabase function to sync auth users with your users table:

```sql
-- Create a function to handle new user signups
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if the user already exists in our users table
  IF NOT EXISTS (SELECT 1 FROM users WHERE email = NEW.email) THEN
    -- Create a new tenant for the user
    INSERT INTO tenants (id, name, email, phone, address, active)
    VALUES (
      uuid_generate_v4(),
      NEW.email, -- Temporary name, can be updated later
      NEW.email,
      '', -- Placeholder phone
      '', -- Placeholder address
      true
    )
    RETURNING id INTO NEW.tenant_id;
    
    -- Create a new user record
    INSERT INTO users (id, tenant_id, first_name, last_name, email, role, active)
    VALUES (
      uuid_generate_v4(),
      NEW.tenant_id,
      '', -- Placeholder first name
      '', -- Placeholder last name
      NEW.email,
      'admin', -- Default role
      true
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to run this function after a new auth.users record is created
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION handle_new_user();
```

## 5. Testing Authentication

1. Register a new user through your application
2. Verify the user appears in Supabase Auth > Users
3. Check that the user and tenant records were created in your database
4. Test login functionality
5. Test protected routes and RLS policies
