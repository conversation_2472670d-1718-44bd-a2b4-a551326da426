import { generateAuthUrl } from '../src/utils/googleCalendar';
import { googleOAuthConfig } from '../src/config/integrations';

// Print the Google OAuth configuration
console.log('Google OAuth Configuration:');
console.log('- Client ID:', googleOAuthConfig.clientId);
console.log('- Redirect URI:', googleOAuthConfig.redirectUri);
console.log('- Scopes:', googleOAuthConfig.scopes);

// Generate and print the authorization URL
const authUrl = generateAuthUrl();
console.log('\nGenerated Authorization URL:');
console.log(authUrl);

// Extract and print the redirect_uri parameter from the URL
if (authUrl) {
  const url = new URL(authUrl);
  const redirectUri = url.searchParams.get('redirect_uri');
  console.log('\nRedirect URI from URL:');
  console.log(redirectUri);
}
