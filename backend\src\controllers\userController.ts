import { Request, Response } from 'express';
import { UserRole } from '../models/supabase';
import { User as UserType } from '../models';
import { User } from '../utils/modelHelpers';
import { QueryOptions } from '../utils/modelHelpers';

// Get all users for a tenant
export const getUsers = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.params;
    const { role } = req.query;

    // Check if user has access to this tenant
    if (req.user.tenantId !== tenantId && req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to access users for this tenant' });
    }

    // Build query
    const query: QueryOptions = {
      where: {
        tenantId,
        active: true, // Only return active users by default
      },
      attributes: ['id', 'firstName', 'lastName', 'email', 'phone', 'role', 'active', 'createdAt', 'updatedAt'],
    };

    // Filter by role if provided
    if (role && query.where) {
      query.where.role = role as UserRole;
    }

    const users = await User.findAll(query);

    res.json(users);
  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({ message: 'Failed to get users' });
  }
};

// Get user by ID
export const getUserById = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId, userId } = req.params;

    // Check if user has access to this tenant
    if (req.user.tenantId !== tenantId && req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to access users for this tenant' });
    }

    const user = await User.findOne({
      where: {
        id: userId,
        tenantId,
        active: true, // Only return active users
      },
      attributes: ['id', 'firstName', 'lastName', 'email', 'phone', 'role', 'active', 'createdAt', 'updatedAt'],
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    res.json(user);
  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({ message: 'Failed to get user' });
  }
};

// Create a new user
export const createUser = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.params;
    const { firstName, lastName, email, password, phone, role } = req.body;

    // Check if user has admin access to this tenant
    if (req.user.tenantId !== tenantId || req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to create users for this tenant' });
    }

    // Check if user with email already exists for this tenant
    const existingUser = await User.findOne({
      where: {
        email,
        tenantId,
      },
    });

    if (existingUser) {
      return res.status(400).json({ message: 'User with this email already exists' });
    }

    // Create new user
    const userData: Partial<UserType> = {
      tenantId,
      firstName,
      lastName,
      email,
      password,
      phone,
      role: role || UserRole.STAFF,
      active: true,
    };

    const user = await User.create(userData);

    // Convert to UserType to access properties
    const userResponse = user as unknown as UserType;

    res.status(201).json({
      message: 'User created successfully',
      user: {
        id: userResponse.id,
        firstName: userResponse.firstName,
        lastName: userResponse.lastName,
        email: userResponse.email,
        phone: userResponse.phone,
        role: userResponse.role,
      },
    });
  } catch (error) {
    console.error('Create user error:', error);
    res.status(500).json({ message: 'Failed to create user' });
  }
};

// Update a user
export const updateUser = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId, userId } = req.params;
    const { firstName, lastName, email, phone, role, active } = req.body;

    // Check if user has admin access to this tenant or is updating their own profile
    const isSelfUpdate = req.user.id === userId;
    const isAdmin = req.user.role === UserRole.ADMIN && req.user.tenantId === tenantId;

    if (!isSelfUpdate && !isAdmin) {
      return res.status(403).json({ message: 'Not authorized to update this user' });
    }

    const user = await User.findOne({
      where: {
        id: userId,
        tenantId,
      },
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Build update object
    const updateData: Partial<UserType> = {};

    if (firstName) updateData.firstName = firstName;
    if (lastName) updateData.lastName = lastName;
    if (phone) updateData.phone = phone;

    // Only admin can update these fields
    if (isAdmin) {
      if (email) updateData.email = email;
      if (role) updateData.role = role;
      if (active !== undefined) updateData.active = active;
    }

    // Update user
    const updatedUser = await User.update(
      updateData,
      {
        where: {
          id: userId
        }
      }
    );

    // Get the updated user data
    const refreshedUser = await User.findOne({
      where: {
        id: userId,
        tenantId,
      },
      attributes: ['id', 'firstName', 'lastName', 'email', 'phone', 'role', 'active', 'createdAt', 'updatedAt'],
    });

    if (!refreshedUser) {
      return res.status(404).json({ message: 'User not found after update' });
    }

    // Convert to UserType to access properties
    const userResponse = refreshedUser as unknown as UserType;

    res.json({
      message: 'User updated successfully',
      user: {
        id: userResponse.id,
        firstName: userResponse.firstName,
        lastName: userResponse.lastName,
        email: userResponse.email,
        phone: userResponse.phone,
        role: userResponse.role,
        active: userResponse.active,
      },
    });
  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({ message: 'Failed to update user' });
  }
};

// Delete a user
export const deleteUser = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId, userId } = req.params;

    // Check if user has admin access to this tenant
    if (req.user.tenantId !== tenantId || req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to delete users for this tenant' });
    }

    // Prevent deleting yourself
    if (req.user.id === userId) {
      return res.status(400).json({ message: 'Cannot delete your own account' });
    }

    const user = await User.findOne({
      where: {
        id: userId,
        tenantId,
      },
    });

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Instead of hard delete, set active to false
    await User.update(
      { active: false },
      {
        where: {
          id: userId
        }
      }
    );

    res.json({ message: 'User deactivated successfully' });
  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({ message: 'Failed to delete user' });
  }
};
