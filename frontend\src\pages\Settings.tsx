import React, { useState, useEffect } from 'react';
import Layout from '../components/Layout';
import { useAuth } from '../contexts/AuthContext';
import { tenantService } from '../services';
import GoogleCalendarIntegration from '../components/GoogleCalendarIntegration';
import { FiDollarSign, FiClock } from 'react-icons/fi';
import { SUPPORTED_CURRENCIES } from '../utils/currencyUtils';
import { COMMON_TIMEZONES, getTimezonesByRegion } from '../utils/timezoneUtils';

const Settings: React.FC = () => {
  const { tenant, user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    address: '',
    logo: '',
    primaryColor: '#3B82F6',
    secondaryColor: '#1E3A8A',
    welcomeMessage: 'Welcome to our appointment scheduling system!',
    currency: 'INR',
    timezone: 'Asia/Kolkata'
  });

  useEffect(() => {
    const fetchTenantDetails = async () => {
      if (!tenant || !user) return;

      setIsLoading(true);
      setError(null);

      try {
        const tenantData = await tenantService.getTenant(tenant.id);

        setFormData({
          name: tenantData.name || '',
          email: tenantData.email || '',
          phone: tenantData.phone || '',
          address: tenantData.address || '',
          logo: tenantData.logo || '',
          primaryColor: tenantData.primaryColor || '#3B82F6',
          secondaryColor: tenantData.secondaryColor || '#1E3A8A',
          welcomeMessage: tenantData.welcomeMessage || 'Welcome to our appointment scheduling system!',
          currency: tenantData.currency || 'INR',
          timezone: tenantData.timezone || 'Asia/Kolkata'
        });
      } catch (err: any) {
        console.error('Error fetching tenant details:', err);
        setError('Failed to load tenant details. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchTenantDetails();
  }, [tenant, user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!tenant) return;

    setIsSaving(true);
    setError(null);
    setSuccess(null);

    try {
      await tenantService.updateTenant(tenant.id, formData);
      setSuccess('Settings updated successfully!');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err: any) {
      console.error('Error updating tenant:', err);
      setError('Failed to update settings. Please try again later.');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center min-h-[60vh]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-800 mb-8">Business Settings</h1>

        {error && (
          <div className="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            {success}
          </div>
        )}

        <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Business Information</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="name" className="block text-gray-700 font-medium mb-2">
                  Business Name *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-gray-700 font-medium mb-2">
                  Business Email *
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="phone" className="block text-gray-700 font-medium mb-2">
                  Business Phone *
                </label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label htmlFor="address" className="block text-gray-700 font-medium mb-2">
                  Business Address *
                </label>
                <input
                  type="text"
                  id="address"
                  name="address"
                  value={formData.address}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>
            </div>

            <div className="mb-6">
              <label htmlFor="logo" className="block text-gray-700 font-medium mb-2">
                Logo URL
              </label>
              <input
                type="url"
                id="logo"
                name="logo"
                value={formData.logo}
                onChange={handleInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="https://example.com/logo.png"
              />
              <p className="mt-1 text-sm text-gray-500">
                Enter a URL to your logo image. Leave blank to use the default logo.
              </p>
            </div>
          </div>

          <div className="border-t border-gray-200 p-6">
            <h2 className="text-xl font-semibold mb-4">Appearance & Preferences</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="primaryColor" className="block text-gray-700 font-medium mb-2">
                  Primary Color
                </label>
                <div className="flex items-center">
                  <input
                    type="color"
                    id="primaryColor"
                    name="primaryColor"
                    value={formData.primaryColor}
                    onChange={handleInputChange}
                    className="w-12 h-10 border border-gray-300 rounded-md mr-2"
                  />
                  <input
                    type="text"
                    value={formData.primaryColor}
                    onChange={handleInputChange}
                    name="primaryColor"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="secondaryColor" className="block text-gray-700 font-medium mb-2">
                  Secondary Color
                </label>
                <div className="flex items-center">
                  <input
                    type="color"
                    id="secondaryColor"
                    name="secondaryColor"
                    value={formData.secondaryColor}
                    onChange={handleInputChange}
                    className="w-12 h-10 border border-gray-300 rounded-md mr-2"
                  />
                  <input
                    type="text"
                    value={formData.secondaryColor}
                    onChange={handleInputChange}
                    name="secondaryColor"
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            <div className="mb-6">
              <label htmlFor="welcomeMessage" className="block text-gray-700 font-medium mb-2">
                Welcome Message
              </label>
              <textarea
                id="welcomeMessage"
                name="welcomeMessage"
                value={formData.welcomeMessage}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              ></textarea>
              <p className="mt-1 text-sm text-gray-500">
                This message will be displayed on your booking page.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="currency" className="block text-gray-700 font-medium mb-2 flex items-center">
                  <FiDollarSign className="mr-1" /> Currency
                </label>
                <select
                  id="currency"
                  name="currency"
                  value={formData.currency}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {SUPPORTED_CURRENCIES.map((currency) => (
                    <option key={currency.code} value={currency.code}>
                      {currency.name} ({currency.symbol})
                    </option>
                  ))}
                </select>
                <p className="mt-1 text-sm text-gray-500">
                  Select the currency to use for all prices and payments.
                </p>
              </div>

              <div>
                <label htmlFor="timezone" className="block text-gray-700 font-medium mb-2 flex items-center">
                  <FiClock className="mr-1" /> Timezone
                </label>
                <select
                  id="timezone"
                  name="timezone"
                  value={formData.timezone}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <optgroup label="Asia">
                    {COMMON_TIMEZONES.filter(tz => tz.region === 'Asia').map((tz) => (
                      <option key={tz.value} value={tz.value}>
                        {tz.label} ({tz.offset})
                      </option>
                    ))}
                  </optgroup>
                  <optgroup label="Europe">
                    {COMMON_TIMEZONES.filter(tz => tz.region === 'Europe').map((tz) => (
                      <option key={tz.value} value={tz.value}>
                        {tz.label} ({tz.offset})
                      </option>
                    ))}
                  </optgroup>
                  <optgroup label="North America">
                    {COMMON_TIMEZONES.filter(tz => tz.region === 'North America').map((tz) => (
                      <option key={tz.value} value={tz.value}>
                        {tz.label} ({tz.offset})
                      </option>
                    ))}
                  </optgroup>
                  <optgroup label="Australia & Pacific">
                    {COMMON_TIMEZONES.filter(tz => tz.region === 'Australia & Pacific').map((tz) => (
                      <option key={tz.value} value={tz.value}>
                        {tz.label} ({tz.offset})
                      </option>
                    ))}
                  </optgroup>
                  <optgroup label="South America">
                    {COMMON_TIMEZONES.filter(tz => tz.region === 'South America').map((tz) => (
                      <option key={tz.value} value={tz.value}>
                        {tz.label} ({tz.offset})
                      </option>
                    ))}
                  </optgroup>
                  <optgroup label="Africa">
                    {COMMON_TIMEZONES.filter(tz => tz.region === 'Africa').map((tz) => (
                      <option key={tz.value} value={tz.value}>
                        {tz.label} ({tz.offset})
                      </option>
                    ))}
                  </optgroup>
                </select>
                <p className="mt-1 text-sm text-gray-500">
                  Select the timezone for appointment scheduling and display.
                </p>
              </div>
            </div>
          </div>

          <div className="border-t border-gray-200 p-6">
            <div className="flex justify-end">
              <button
                type="submit"
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 flex items-center"
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                  </>
                ) : (
                  'Save Changes'
                )}
              </button>
            </div>
          </div>
        </form>

        <div className="mt-8 bg-white rounded-lg shadow-md overflow-hidden">
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4">Booking Page</h2>
            <p className="mb-4">
              Your public booking page is available at the following URL:
            </p>
            <div className="flex items-center">
              <input
                type="text"
                value={`${window.location.origin}/booking/${tenant?.id}`}
                readOnly
                className="flex-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md focus:outline-none"
              />
              <button
                type="button"
                onClick={() => {
                  navigator.clipboard.writeText(`${window.location.origin}/booking/${tenant?.id}`);
                  setSuccess('Booking URL copied to clipboard!');
                  setTimeout(() => {
                    setSuccess(null);
                  }, 3000);
                }}
                className="ml-2 bg-gray-200 text-gray-800 px-4 py-2 rounded-md hover:bg-gray-300"
              >
                Copy
              </button>
            </div>
          </div>
        </div>

        {/* Google Calendar Integration */}
        <div className="mt-8">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Integrations</h2>
          <GoogleCalendarIntegration />
        </div>
      </div>
    </Layout>
  );
};

export default Settings;
