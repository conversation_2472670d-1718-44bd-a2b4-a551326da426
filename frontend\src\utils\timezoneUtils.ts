// Timezone options
export interface TimezoneOption {
  value: string;
  label: string;
  offset: string;
  region: string;
}

// List of common timezones
export const COMMON_TIMEZONES: TimezoneOption[] = [
  // Asia
  { value: 'Asia/Kolkata', label: 'India (New Delhi)', offset: 'GMT+5:30', region: 'Asia' },
  { value: 'Asia/Dubai', label: 'United Arab Emirates (Dubai)', offset: 'GMT+4:00', region: 'Asia' },
  { value: 'Asia/Singapore', label: 'Singapore', offset: 'GMT+8:00', region: 'Asia' },
  { value: 'Asia/Tokyo', label: 'Japan (Tokyo)', offset: 'GMT+9:00', region: 'Asia' },
  { value: 'Asia/Shanghai', label: 'China (Shanghai)', offset: 'GMT+8:00', region: 'Asia' },
  { value: 'Asia/Hong_Kong', label: 'Hong Kong', offset: 'GMT+8:00', region: 'Asia' },
  { value: 'Asia/Bangkok', label: 'Thailand (Bangkok)', offset: 'GMT+7:00', region: 'Asia' },
  
  // Europe
  { value: 'Europe/London', label: 'United Kingdom (London)', offset: 'GMT+0:00', region: 'Europe' },
  { value: 'Europe/Paris', label: 'France (Paris)', offset: 'GMT+1:00', region: 'Europe' },
  { value: 'Europe/Berlin', label: 'Germany (Berlin)', offset: 'GMT+1:00', region: 'Europe' },
  { value: 'Europe/Moscow', label: 'Russia (Moscow)', offset: 'GMT+3:00', region: 'Europe' },
  
  // North America
  { value: 'America/New_York', label: 'USA (New York)', offset: 'GMT-5:00', region: 'North America' },
  { value: 'America/Chicago', label: 'USA (Chicago)', offset: 'GMT-6:00', region: 'North America' },
  { value: 'America/Denver', label: 'USA (Denver)', offset: 'GMT-7:00', region: 'North America' },
  { value: 'America/Los_Angeles', label: 'USA (Los Angeles)', offset: 'GMT-8:00', region: 'North America' },
  { value: 'America/Toronto', label: 'Canada (Toronto)', offset: 'GMT-5:00', region: 'North America' },
  { value: 'America/Vancouver', label: 'Canada (Vancouver)', offset: 'GMT-8:00', region: 'North America' },
  
  // Australia & Pacific
  { value: 'Australia/Sydney', label: 'Australia (Sydney)', offset: 'GMT+10:00', region: 'Australia & Pacific' },
  { value: 'Australia/Perth', label: 'Australia (Perth)', offset: 'GMT+8:00', region: 'Australia & Pacific' },
  { value: 'Pacific/Auckland', label: 'New Zealand (Auckland)', offset: 'GMT+12:00', region: 'Australia & Pacific' },
  
  // South America
  { value: 'America/Sao_Paulo', label: 'Brazil (São Paulo)', offset: 'GMT-3:00', region: 'South America' },
  { value: 'America/Buenos_Aires', label: 'Argentina (Buenos Aires)', offset: 'GMT-3:00', region: 'South America' },
  
  // Africa
  { value: 'Africa/Cairo', label: 'Egypt (Cairo)', offset: 'GMT+2:00', region: 'Africa' },
  { value: 'Africa/Johannesburg', label: 'South Africa (Johannesburg)', offset: 'GMT+2:00', region: 'Africa' },
  { value: 'Africa/Lagos', label: 'Nigeria (Lagos)', offset: 'GMT+1:00', region: 'Africa' },
];

// Group timezones by region
export const getTimezonesByRegion = () => {
  const regions: { [key: string]: TimezoneOption[] } = {};
  
  COMMON_TIMEZONES.forEach(timezone => {
    if (!regions[timezone.region]) {
      regions[timezone.region] = [];
    }
    regions[timezone.region].push(timezone);
  });
  
  return regions;
};

// Get timezone option by value
export const getTimezoneByValue = (value: string): TimezoneOption | undefined => {
  return COMMON_TIMEZONES.find(tz => tz.value === value);
};

// Format date with timezone
export const formatDateWithTimezone = (date: Date | string, timezone: string = 'Asia/Kolkata'): string => {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    timeZone: timezone,
  };
  
  return new Intl.DateTimeFormat('en-US', options).format(new Date(date));
};

// Format time with timezone
export const formatTimeWithTimezone = (date: Date | string, timezone: string = 'Asia/Kolkata'): string => {
  const options: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    timeZone: timezone,
  };
  
  return new Intl.DateTimeFormat('en-US', options).format(new Date(date));
};

// Get current date in specific timezone
export const getCurrentDateInTimezone = (timezone: string = 'Asia/Kolkata'): Date => {
  const date = new Date();
  const options: Intl.DateTimeFormatOptions = { timeZone: timezone };
  const formatter = new Intl.DateTimeFormat('en-US', options);
  return new Date(formatter.format(date));
};
