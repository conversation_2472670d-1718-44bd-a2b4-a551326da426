import api from './api';
import eventBus, { EVENT_TYPES } from '../utils/eventBus';

export interface ServiceData {
  name: string;
  description?: string;
  duration: number;
  price: number;
  color?: string;
}

const serviceService = {
  // Get all services for a tenant
  getServices: async (tenantId: string) => {
    const response = await api.get(`/services/${tenantId}/services`);
    return response.data;
  },

  // Get service by ID
  getServiceById: async (tenantId: string, serviceId: string) => {
    const response = await api.get(`/services/${tenantId}/services/${serviceId}`);
    return response.data;
  },

  // Create a new service
  createService: async (tenantId: string, data: ServiceData) => {
    const response = await api.post(`/services/${tenantId}/services`, data);
    // Emit events for service creation
    eventBus.emit(EVENT_TYPES.SERVICE_CREATED, response.data);
    eventBus.emit(EVENT_TYPES.DATA_CHANGED);
    return response.data;
  },

  // Update a service
  updateService: async (tenantId: string, serviceId: string, data: Partial<ServiceData>) => {
    const response = await api.put(`/services/${tenantId}/services/${serviceId}`, data);
    // Emit events for service update
    eventBus.emit(EVENT_TYPES.SERVICE_UPDATED, response.data);
    eventBus.emit(EVENT_TYPES.DATA_CHANGED);
    return response.data;
  },

  // Delete a service
  deleteService: async (tenantId: string, serviceId: string) => {
    const response = await api.delete(`/services/${tenantId}/services/${serviceId}`);
    // Emit events for service deletion
    eventBus.emit(EVENT_TYPES.SERVICE_DELETED, { id: serviceId, tenantId });
    eventBus.emit(EVENT_TYPES.DATA_CHANGED);
    return response.data;
  },

  // Assign staff to service
  assignStaffToService: async (tenantId: string, serviceId: string, staffIds: string[]) => {
    const response = await api.post(`/services/${tenantId}/services/${serviceId}/staff`, { staffIds });
    // Emit events for service update
    eventBus.emit(EVENT_TYPES.SERVICE_UPDATED, response.data);
    eventBus.emit(EVENT_TYPES.DATA_CHANGED);
    return response.data;
  },
};

export default serviceService;
