import { createClient } from '@supabase/supabase-js';
import config from '../config';
import { TierLevel, getTierById } from '../models/SubscriptionTier';
import { getUserSubscription } from './subscriptionService';
import { canCreateAppointment, getMonthlyAppointmentCount } from './appointmentService';

// Initialize Supabase client
const supabase = createClient(config.supabase.url, config.supabase.serviceKey);

/**
 * Get the current staff count for a user
 */
export async function getStaffCount(userId: string): Promise<number> {
  try {
    // Query staff members (users with role 'staff' in the tenant)
    const { count, error } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: false })
      .eq('tenant_id', userId)
      .eq('role', 'staff');
    
    if (error) {
      console.error('Error getting staff count:', error);
      return 0;
    }
    
    return count || 0;
  } catch (error) {
    console.error('Error in getStaffCount:', error);
    return 0;
  }
}

/**
 * Check if a user can create more staff members
 */
export async function canCreateStaff(userId: string): Promise<{
  canCreate: boolean;
  currentCount: number;
  maxCount: number | null;
  message?: string;
}> {
  try {
    // Get user's subscription
    const subscription = await getUserSubscription(userId);
    
    if (!subscription) {
      return {
        canCreate: false,
        currentCount: 0,
        maxCount: 0,
        message: 'No active subscription found'
      };
    }
    
    // Get tier details
    const tier = getTierById(subscription.tier_id);
    
    if (!tier) {
      return {
        canCreate: false,
        currentCount: 0,
        maxCount: 0,
        message: 'Invalid subscription tier'
      };
    }
    
    // If tier has unlimited staff, allow creation
    if (tier.maxStaffMembers === null) {
      return {
        canCreate: true,
        currentCount: 0,
        maxCount: null
      };
    }
    
    // Get current staff count
    const currentCount = await getStaffCount(userId);
    
    // Check if user has reached the limit
    const canCreate = currentCount < tier.maxStaffMembers;
    
    return {
      canCreate,
      currentCount,
      maxCount: tier.maxStaffMembers,
      message: canCreate ? undefined : `You have reached the maximum number of staff members (${tier.maxStaffMembers}) for your subscription tier. Please upgrade to add more staff members.`
    };
  } catch (error) {
    console.error('Error in canCreateStaff:', error);
    return {
      canCreate: false,
      currentCount: 0,
      maxCount: 0,
      message: 'An error occurred while checking staff limits'
    };
  }
}

/**
 * Get usage statistics for a user
 */
export async function getUserUsageStats(userId: string): Promise<{
  appointments: {
    current: number;
    max: number | null;
    percentage: number | null;
  };
  staff: {
    current: number;
    max: number | null;
    percentage: number | null;
  };
  tierLevel: TierLevel;
}> {
  try {
    // Get user's subscription
    const subscription = await getUserSubscription(userId);
    
    if (!subscription) {
      return {
        appointments: { current: 0, max: 0, percentage: 0 },
        staff: { current: 0, max: 0, percentage: 0 },
        tierLevel: TierLevel.FREE
      };
    }
    
    // Get tier details
    const tier = getTierById(subscription.tier_id);
    
    if (!tier) {
      return {
        appointments: { current: 0, max: 0, percentage: 0 },
        staff: { current: 0, max: 0, percentage: 0 },
        tierLevel: TierLevel.FREE
      };
    }
    
    // Get current month's appointment count
    const appointmentCount = await getMonthlyAppointmentCount(userId);
    
    // Get current staff count
    const staffCount = await getStaffCount(userId);
    
    // Calculate percentages
    const appointmentPercentage = tier.maxAppointmentsPerMonth !== null
      ? Math.round((appointmentCount / tier.maxAppointmentsPerMonth) * 100)
      : null;
    
    const staffPercentage = tier.maxStaffMembers !== null
      ? Math.round((staffCount / tier.maxStaffMembers) * 100)
      : null;
    
    return {
      appointments: {
        current: appointmentCount,
        max: tier.maxAppointmentsPerMonth,
        percentage: appointmentPercentage
      },
      staff: {
        current: staffCount,
        max: tier.maxStaffMembers,
        percentage: staffPercentage
      },
      tierLevel: subscription.tier_id
    };
  } catch (error) {
    console.error('Error in getUserUsageStats:', error);
    return {
      appointments: { current: 0, max: 0, percentage: 0 },
      staff: { current: 0, max: 0, percentage: 0 },
      tierLevel: TierLevel.FREE
    };
  }
}
