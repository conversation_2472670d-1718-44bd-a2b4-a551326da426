import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout';
import { useAuth } from '../contexts/AuthContext';
import { userService, UserRole } from '../services';
import Modal from '../components/Modal';

const Staff: React.FC = () => {
  const { tenant } = useAuth();
  const [staff, setStaff] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [staffToDelete, setStaffToDelete] = useState<any>(null);

  useEffect(() => {
    const fetchStaff = async () => {
      if (!tenant) return;

      setIsLoading(true);
      setError(null);

      try {
        const data = await userService.getUsers(tenant.id, UserRole.STAFF);
        setStaff(data);
      } catch (err: any) {
        setError('Failed to load staff members. Please try again later.');
        console.error('Error fetching staff:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchStaff();
  }, [tenant]);

  const handleDeleteClick = (staffMember: any) => {
    setStaffToDelete(staffMember);
    setIsDeleteModalOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!tenant || !staffToDelete) return;

    try {
      await userService.deleteUser(tenant.id, staffToDelete.id);

      // Remove the deleted staff member from the state
      setStaff(staff.filter(s => s.id !== staffToDelete.id));

      // Show success message
      setSuccess(`${staffToDelete.firstName} ${staffToDelete.lastName} has been deleted successfully.`);

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);

      // Close the modal
      setIsDeleteModalOpen(false);
      setStaffToDelete(null);
    } catch (err: any) {
      console.error('Error deleting staff member:', err);
      setError('Failed to delete staff member. Please try again later.');
      setIsDeleteModalOpen(false);
    }
  };

  return (
    <Layout>
      <div className="max-w-6xl mx-auto">
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800">Staff</h1>
          <Link
            to="/staff/new"
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            Add Staff Member
          </Link>
        </div>

        {error && (
          <div className="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {success && (
          <div className="mb-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            {success}
          </div>
        )}

        {isLoading ? (
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : staff.length === 0 ? (
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <p className="text-gray-500 mb-4">No staff members found</p>
            <p className="text-gray-600">
              Get started by adding your first staff member.
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {staff.map((staffMember) => (
              <div
                key={staffMember.id}
                className="bg-white rounded-lg shadow-md overflow-hidden"
              >
                <div className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="bg-blue-100 text-blue-800 rounded-full h-12 w-12 flex items-center justify-center font-bold text-xl">
                      {staffMember.firstName.charAt(0)}
                      {staffMember.lastName.charAt(0)}
                    </div>
                    <div className="ml-4">
                      <h2 className="text-xl font-semibold text-gray-800">
                        {staffMember.firstName} {staffMember.lastName}
                      </h2>
                      <p className="text-gray-600">{staffMember.email}</p>
                    </div>
                  </div>
                  {staffMember.phone && (
                    <p className="text-gray-600 mb-4">
                      <span className="font-medium">Phone:</span> {staffMember.phone}
                    </p>
                  )}
                  <div className="flex justify-end space-x-2">
                    <Link
                      to={`/staff/${staffMember.id}`}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      View
                    </Link>
                    <Link
                      to={`/staff/${staffMember.id}/edit`}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      Edit
                    </Link>
                    <Link
                      to={`/staff/${staffMember.id}/availability`}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      Availability
                    </Link>
                    <button
                      onClick={() => handleDeleteClick(staffMember)}
                      className="text-red-600 hover:text-red-800"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Confirm Delete"
        footer={
          <div className="flex justify-end space-x-2">
            <button
              onClick={() => setIsDeleteModalOpen(false)}
              className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300"
            >
              Cancel
            </button>
            <button
              onClick={handleDeleteConfirm}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
            >
              Delete
            </button>
          </div>
        }
      >
        <p className="text-gray-700">
          Are you sure you want to delete {staffToDelete?.firstName} {staffToDelete?.lastName}?
        </p>
        <p className="text-gray-500 mt-2">
          This action cannot be undone. All appointments and data associated with this staff member will be permanently removed.
        </p>
      </Modal>
    </Layout>
  );
};

export default Staff;
