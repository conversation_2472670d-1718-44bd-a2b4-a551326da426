import api from './api';
import { NotificationType, NotificationChannel } from './types';

export interface Notification {
  id: string;
  userId: string;
  appointmentId?: string;
  type: NotificationType;
  channel: NotificationChannel;
  content: string;
  sent: boolean;
  sentAt?: string;
  error?: string;
  createdAt: string;
  updatedAt: string;
  appointment?: {
    id: string;
    startTime: string;
    endTime: string;
    status: string;
    service: {
      id: string;
      name: string;
      price: number;
    };
    staff: {
      id: string;
      firstName: string;
      lastName: string;
    };
  };
}

const notificationService = {
  // Get notifications for a user
  getUserNotifications: async (tenantId: string, userId: string) => {
    const response = await api.get(`/notifications/${tenantId}/users/${userId}/notifications`);
    return response.data as Notification[];
  },

  // Mark notification as read
  markNotificationAsRead: async (tenantId: string, notificationId: string) => {
    const response = await api.put(`/notifications/${tenantId}/notifications/${notificationId}/read`);
    return response.data;
  },

  // Send appointment reminders (admin only)
  sendAppointmentReminders: async (tenantId: string) => {
    const response = await api.post(`/notifications/${tenantId}/reminders/send`);
    return response.data;
  },

  // Get recent activities
  getRecentActivities: async (tenantId: string, limit: number = 5) => {
    // This combines notifications and appointments to create a comprehensive activity feed
    try {
      // Get recent notifications
      const notifications = await api.get(`/notifications/${tenantId}/recent?limit=${limit}`);
      
      // If the endpoint doesn't exist yet, we'll simulate it with local data
      // In a real implementation, this would be handled by the backend
      return notifications.data;
    } catch (error) {
      console.error('Error fetching recent activities:', error);
      
      // Fallback to local data if the endpoint doesn't exist
      return [];
    }
  }
};

export default notificationService;
