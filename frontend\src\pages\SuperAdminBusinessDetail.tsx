import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, Link, useNavigate } from 'react-router-dom';
import Layout from '../components/Layout';
import { superAdminService } from '../services';
import {
  FiArrowLeft,
  FiGrid,
  FiUsers,
  FiCalendar,
  FiPackage,
  FiCheckCircle,
  FiXCircle,
  FiAlertTriangle,
  FiTrash2
} from 'react-icons/fi';

interface TenantDetails {
  tenant: {
    id: string;
    name: string;
    email: string;
    phone: string;
    address: string;
    active: boolean;
    createdAt: string;
    timezone?: string;
    currency?: string;
  };
  stats: {
    users: number;
    appointments: number;
    services: number;
  };
}

const SuperAdminBusinessDetail: React.FC = () => {
  const { tenantId } = useParams<{ tenantId: string }>();
  const navigate = useNavigate();
  const [tenantDetails, setTenantDetails] = useState<TenantDetails | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateMessage, setUpdateMessage] = useState<{type: 'success' | 'error', text: string} | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    if (tenantId) {
      fetchTenantDetails();
    }
  }, [tenantId]);

  const fetchTenantDetails = async () => {
    if (!tenantId) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      const data = await superAdminService.getTenantDetails(tenantId);
      setTenantDetails(data);
    } catch (err: any) {
      setError('Failed to load business details. Please try again later.');
      console.error('Error fetching business details:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStatusChange = async (active: boolean) => {
    if (!tenantId || !tenantDetails) return;
    
    try {
      setIsUpdating(true);
      setUpdateMessage(null);
      
      await superAdminService.updateTenantStatus(tenantId, active);
      
      // Update local state
      setTenantDetails({
        ...tenantDetails,
        tenant: {
          ...tenantDetails.tenant,
          active
        }
      });
      
      setUpdateMessage({
        type: 'success',
        text: `Business ${active ? 'activated' : 'deactivated'} successfully`
      });
    } catch (err: any) {
      setUpdateMessage({
        type: 'error',
        text: `Failed to ${active ? 'activate' : 'deactivate'} business. Please try again.`
      });
      console.error('Error updating business status:', err);
    } finally {
      setIsUpdating(false);
      // Clear message after 3 seconds
      setTimeout(() => setUpdateMessage(null), 3000);
    }
  };

  const handleDeleteTenant = async () => {
    if (!tenantId) return;
    
    try {
      setIsUpdating(true);
      setUpdateMessage(null);
      
      await superAdminService.deleteTenant(tenantId);
      
      setUpdateMessage({
        type: 'success',
        text: 'Business deleted successfully'
      });
      
      // Redirect after a short delay
      setTimeout(() => {
        navigate('/super-admin/businesses');
      }, 1500);
    } catch (err: any) {
      setUpdateMessage({
        type: 'error',
        text: 'Failed to delete business. Please try again.'
      });
      console.error('Error deleting business:', err);
      setIsUpdating(false);
      setShowDeleteConfirm(false);
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </Layout>
    );
  }

  if (!tenantDetails && !isLoading) {
    return (
      <Layout>
        <div className="container mx-auto px-4 py-8">
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
            <div className="flex items-center">
              <FiAlertTriangle className="mr-2" />
              <p>Business not found or you don't have permission to view it.</p>
            </div>
          </div>
          <Link to="/super-admin/businesses" className="inline-flex items-center text-blue-600 hover:text-blue-800">
            <FiArrowLeft className="mr-2" /> Back to Businesses
          </Link>
        </div>
      </Layout>
    );
  }

  const { tenant, stats } = tenantDetails!;

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-6">
          <Link to="/super-admin/businesses" className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-4">
            <FiArrowLeft className="mr-2" /> Back to Businesses
          </Link>
          <h1 className="text-3xl font-bold mb-2">{tenant.name}</h1>
          <div className="flex items-center">
            <p className="text-gray-600 mr-3">Business Details</p>
            {tenant.active ? (
              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                Active
              </span>
            ) : (
              <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                Inactive
              </span>
            )}
          </div>
        </div>

        {error && (
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
            <div className="flex items-center">
              <FiAlertTriangle className="mr-2" />
              <p>{error}</p>
            </div>
          </div>
        )}

        {updateMessage && (
          <div 
            className={`${
              updateMessage.type === 'success' ? 'bg-green-100 border-green-500 text-green-700' : 'bg-red-100 border-red-500 text-red-700'
            } border-l-4 p-4 mb-6`} 
            role="alert"
          >
            <div className="flex items-center">
              {updateMessage.type === 'success' ? <FiCheckCircle className="mr-2" /> : <FiAlertTriangle className="mr-2" />}
              <p>{updateMessage.text}</p>
            </div>
          </div>
        )}

        {/* Business Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-100">
            <div className="flex items-center mb-4">
              <div className="p-3 rounded-lg bg-blue-100 mr-3">
                <FiUsers className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-700">Users</h3>
            </div>
            <p className="text-3xl font-bold text-gray-800">{stats.users}</p>
            <div className="mt-2 text-sm text-gray-500">
              Total users in this business
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-100">
            <div className="flex items-center mb-4">
              <div className="p-3 rounded-lg bg-purple-100 mr-3">
                <FiCalendar className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-700">Appointments</h3>
            </div>
            <p className="text-3xl font-bold text-gray-800">{stats.appointments}</p>
            <div className="mt-2 text-sm text-gray-500">
              Total appointments
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-100">
            <div className="flex items-center mb-4">
              <div className="p-3 rounded-lg bg-green-100 mr-3">
                <FiPackage className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-700">Services</h3>
            </div>
            <p className="text-3xl font-bold text-gray-800">{stats.services}</p>
            <div className="mt-2 text-sm text-gray-500">
              Total services offered
            </div>
          </div>
        </div>

        {/* Business Details */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100 mb-8">
          <div className="px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
            <div className="flex items-center">
              <FiGrid className="h-5 w-5 text-blue-600 mr-2" />
              <h2 className="text-xl font-bold text-gray-800">Business Information</h2>
            </div>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">Business Name</h3>
                <p className="text-base text-gray-900">{tenant.name}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">Email</h3>
                <p className="text-base text-gray-900">{tenant.email}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">Phone</h3>
                <p className="text-base text-gray-900">{tenant.phone || 'N/A'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">Address</h3>
                <p className="text-base text-gray-900">{tenant.address || 'N/A'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">Created On</h3>
                <p className="text-base text-gray-900">{new Date(tenant.createdAt).toLocaleDateString()}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">Status</h3>
                <p className="text-base text-gray-900">
                  {tenant.active ? 'Active' : 'Inactive'}
                </p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">Timezone</h3>
                <p className="text-base text-gray-900">{tenant.timezone || 'Default (Asia/Kolkata)'}</p>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-500 mb-1">Currency</h3>
                <p className="text-base text-gray-900">{tenant.currency || 'Default (INR)'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100">
          <div className="px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
            <div className="flex items-center">
              <h2 className="text-xl font-bold text-gray-800">Actions</h2>
            </div>
          </div>
          <div className="p-6">
            <div className="flex flex-wrap gap-4">
              {tenant.active ? (
                <button
                  onClick={() => handleStatusChange(false)}
                  disabled={isUpdating}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 disabled:opacity-50"
                >
                  <FiXCircle className="mr-2" />
                  Deactivate Business
                </button>
              ) : (
                <button
                  onClick={() => handleStatusChange(true)}
                  disabled={isUpdating}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                >
                  <FiCheckCircle className="mr-2" />
                  Activate Business
                </button>
              )}
              
              {!showDeleteConfirm ? (
                <button
                  onClick={() => setShowDeleteConfirm(true)}
                  disabled={isUpdating}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                >
                  <FiTrash2 className="mr-2" />
                  Delete Business
                </button>
              ) : (
                <div className="flex items-center gap-2">
                  <span className="text-sm text-red-600 font-medium">Are you sure?</span>
                  <button
                    onClick={handleDeleteTenant}
                    disabled={isUpdating}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    Yes, Delete
                  </button>
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    disabled={isUpdating}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    Cancel
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default SuperAdminBusinessDetail;
