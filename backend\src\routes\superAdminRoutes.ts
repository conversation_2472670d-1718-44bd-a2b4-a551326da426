import express from 'express';
import { authenticate, authorize } from '../middleware/auth';
import { UserRole } from '../models/supabase';
import {
  getAllTenants,
  getTenantDetails,
  updateTenantStatus,
  deleteTenant,
  getSuperAdminDashboard
} from '../controllers/superAdminController';

const router = express.Router();

// All routes protected and limited to super admin
router.get('/tenants', authenticate as any, authorize(UserRole.SUPER_ADMIN) as any, getAllTenants as any);
router.get('/tenants/:tenantId', authenticate as any, authorize(UserRole.SUPER_ADMIN) as any, getTenantDetails as any);
router.put('/tenants/:tenantId/status', authenticate as any, authorize(UserRole.SUPER_ADMIN) as any, updateTenantStatus as any);
router.delete('/tenants/:tenantId', authenticate as any, authorize(UserRole.SUPER_ADMIN) as any, deleteTenant as any);
router.get('/dashboard', authenticate as any, authorize(UserRole.SUPER_ADMIN) as any, getSuperAdminDashboard as any);

export default router;