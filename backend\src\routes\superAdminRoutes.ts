import express from 'express';
import { authenticate, authorize } from '../middleware/auth';
import { UserRole } from '../models/supabase';
import {
  getAllTenants,
  getTenantDetails,
  updateTenantStatus,
  deleteTenant,
  getSuperAdminDashboard
} from '../controllers/superAdminController';

const router = express.Router();

// Debug middleware to log all requests to this router
router.use((req, res, next) => {
  console.log(`Super Admin Route: ${req.method} ${req.path}`);
  next();
});

// Simple test route
router.get('/test', (req, res) => {
  res.json({ message: 'Super admin routes working!' });
});

// All routes protected and limited to super admin
router.get('/tenants', authenticate, authorize(UserRole.SUPER_ADMIN), getAllTenants as any);
router.get('/tenants/:tenantId', authenticate, authorize(UserRole.SUPER_ADMIN), getTenantDetails as any);
router.put('/tenants/:tenantId/status', authenticate, authorize(UserRole.SUPER_ADMIN), updateTenantStatus as any);
router.delete('/tenants/:tenantId', authenticate, authorize(UserRole.SUPER_ADMIN), deleteTenant as any);
router.get('/dashboard', authenticate, authorize(UserRole.SUPER_ADMIN), getSuperAdminDashboard as any);

export default router;