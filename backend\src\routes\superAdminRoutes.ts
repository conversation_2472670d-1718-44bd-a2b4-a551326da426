import express from 'express';
import { authenticate, authorize } from '../middleware/auth';
import { UserRole } from '../models/supabase';
import {
  getAllTenants,
  getTenantDetails,
  updateTenantStatus,
  deleteTenant,
  getSuperAdminDashboard
} from '../controllers/superAdminController';

const router = express.Router();

// All routes protected and limited to super admin
import { asyncHandler } from '../utils/routeHandler';
// ...existing code...
router.get('/tenants', authenticate as any, authorize(UserRole.SUPER_ADMIN) as any, asyncHandler(getAllTenants));
router.get('/tenants/:tenantId', authenticate as any, authorize(UserRole.SUPER_ADMIN) as any, asyncHandler(getTenantDetails));
router.put('/tenants/:tenantId/status', authenticate as any, authorize(UserRole.SUPER_ADMIN) as any, asyncHandler(updateTenantStatus));
router.delete('/tenants/:tenantId', authenticate as any, authorize(UserRole.SUPER_ADMIN) as any, asyncHandler(deleteTenant));
router.get('/dashboard', authenticate as any, authorize(UserRole.SUPER_ADMIN) as any, asyncHandler(getSuperAdminDashboard));

export default router;