import React, { useState, useRef, useEffect } from 'react';

interface ColorPickerProps {
  color: string;
  onChange: (color: string) => void;
}

export const ColorPicker: React.FC<ColorPickerProps> = ({ color, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentColor, setCurrentColor] = useState(color);
  const pickerRef = useRef<HTMLDivElement>(null);

  // Update internal state when prop changes
  useEffect(() => {
    setCurrentColor(color);
  }, [color]);

  // Handle clicks outside the color picker
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (pickerRef.current && !pickerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Handle color change
  const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = e.target.value;
    setCurrentColor(newColor);
    onChange(newColor);
  };

  // Predefined colors
  const predefinedColors = [
    '#3b82f6', // Blue
    '#10b981', // Green
    '#f59e0b', // Yellow
    '#ef4444', // Red
    '#8b5cf6', // Purple
    '#ec4899', // Pink
    '#6b7280', // Gray
    '#000000', // Black
  ];

  return (
    <div className="relative" ref={pickerRef}>
      <div className="flex items-center space-x-2">
        <button
          type="button"
          className="flex h-8 w-8 items-center justify-center rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          style={{ backgroundColor: currentColor }}
          onClick={() => setIsOpen(!isOpen)}
        >
          <span className="sr-only">Open color picker</span>
        </button>
        <input
          type="text"
          value={currentColor}
          onChange={(e) => {
            setCurrentColor(e.target.value);
            onChange(e.target.value);
          }}
          className="block w-24 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
        />
      </div>

      {isOpen && (
        <div className="absolute left-0 top-full z-10 mt-2 w-56 rounded-md bg-white p-4 shadow-lg ring-1 ring-black ring-opacity-5">
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700">
              Pick a color
            </label>
            <input
              type="color"
              value={currentColor}
              onChange={handleColorChange}
              className="mt-1 h-8 w-full cursor-pointer rounded-md border border-gray-300"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700">
              Preset colors
            </label>
            <div className="mt-2 grid grid-cols-4 gap-2">
              {predefinedColors.map((presetColor) => (
                <button
                  key={presetColor}
                  type="button"
                  className="h-6 w-6 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  style={{ backgroundColor: presetColor }}
                  onClick={() => {
                    setCurrentColor(presetColor);
                    onChange(presetColor);
                  }}
                >
                  <span className="sr-only">Select color {presetColor}</span>
                </button>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
