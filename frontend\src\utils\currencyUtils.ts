// Currency options
export interface CurrencyOption {
  code: string;
  name: string;
  symbol: string;
  locale: string;
}

// List of supported currencies
export const SUPPORTED_CURRENCIES: CurrencyOption[] = [
  {
    code: 'INR',
    name: 'Indian Rupee',
    symbol: '₹',
    locale: 'en-IN'
  },
  {
    code: 'USD',
    name: 'US Dollar',
    symbol: '$',
    locale: 'en-US'
  },
  {
    code: 'EUR',
    name: 'Euro',
    symbol: '€',
    locale: 'en-EU'
  },
  {
    code: 'GBP',
    name: 'British Pound',
    symbol: '£',
    locale: 'en-GB'
  },
  {
    code: 'JPY',
    name: 'Japanese Yen',
    symbol: '¥',
    locale: 'ja-JP'
  },
  {
    code: 'AUD',
    name: 'Australian Dollar',
    symbol: 'A$',
    locale: 'en-AU'
  },
  {
    code: 'CAD',
    name: 'Canadian Dollar',
    symbol: 'C$',
    locale: 'en-CA'
  },
  {
    code: 'SGD',
    name: 'Singapore Dollar',
    symbol: 'S$',
    locale: 'en-SG'
  },
  {
    code: 'CNY',
    name: 'Chinese Yuan',
    symbol: '¥',
    locale: 'zh-CN'
  },
  {
    code: 'AED',
    name: 'UAE Dirham',
    symbol: 'د.إ',
    locale: 'ar-AE'
  }
];

// Get currency option by code
export const getCurrencyByCode = (code: string): CurrencyOption => {
  const currency = SUPPORTED_CURRENCIES.find(c => c.code === code);
  return currency || SUPPORTED_CURRENCIES[0]; // Default to INR if not found
};

// Format price based on currency code
export const formatPrice = (price: number, currencyCode: string = 'INR'): string => {
  const currency = getCurrencyByCode(currencyCode);
  
  return new Intl.NumberFormat(currency.locale, {
    style: 'currency',
    currency: currency.code,
  }).format(price);
};

// Get currency symbol
export const getCurrencySymbol = (currencyCode: string = 'INR'): string => {
  const currency = getCurrencyByCode(currencyCode);
  return currency.symbol;
};
