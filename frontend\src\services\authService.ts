import api from './api';

export interface LoginCredentials {
  email: string;
  password: string;
  tenantId?: string;
}

export interface RegisterTenantData {
  name: string;
  email: string;
  password: string;
  phone: string;
  address: string;
  firstName: string;
  lastName: string;
}

export interface RegisterClientData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  phone?: string;
  tenantId: string;
}

export interface UpdatePasswordData {
  currentPassword: string;
  newPassword: string;
}

export interface ForgotPasswordData {
  email: string;
}

export interface ResetPasswordData {
  token: string;
  newPassword: string;
}

const authService = {
  // Login user
  login: async (credentials: LoginCredentials) => {
    const response = await api.post('/auth/login', credentials);
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
      if (response.data.tenant) {
        localStorage.setItem('tenant', JSON.stringify(response.data.tenant));
      }
    }
    return response.data;
  },

  // Register tenant (business)
  registerTenant: async (data: RegisterTenantData) => {
    const response = await api.post('/auth/register/tenant', data);
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
      localStorage.setItem('tenant', JSON.stringify(response.data.tenant));
    }
    return response.data;
  },

  // Register client
  registerClient: async (data: RegisterClientData) => {
    const response = await api.post('/auth/register/client', data);
    if (response.data.token) {
      localStorage.setItem('token', response.data.token);
      localStorage.setItem('user', JSON.stringify(response.data.user));
    }
    return response.data;
  },

  // Get current user profile
  getProfile: async () => {
    const response = await api.get('/auth/profile');
    return response.data.data;
  },

  // Update password
  updatePassword: async (data: UpdatePasswordData) => {
    const response = await api.put('/auth/password', data);
    return response.data;
  },

  // Request password reset
  forgotPassword: async (data: ForgotPasswordData) => {
    const response = await api.post('/auth/forgot-password', data);
    return response.data;
  },

  // Reset password with token
  resetPassword: async (data: ResetPasswordData) => {
    const response = await api.post('/auth/reset-password', data);
    return response.data;
  },

  // Logout user
  logout: () => {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('tenant');
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    return !!localStorage.getItem('token');
  },

  // Get current user
  getCurrentUser: () => {
    const user = localStorage.getItem('user');
    return user ? JSON.parse(user) : null;
  },

  // Get current tenant
  getCurrentTenant: () => {
    const tenant = localStorage.getItem('tenant');
    return tenant ? JSON.parse(tenant) : null;
  },
};

export default authService;
