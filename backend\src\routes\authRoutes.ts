import express, { Request, Response, NextFunction } from 'express';
import {
  registerTenant,
  getProfile,
  login,
  changePassword,
  requestPasswordReset,
  resetPassword
} from '../controllers/supabaseAuthController';
import { authenticate } from '../middleware/auth';

const router = express.Router();

// Public routes
router.post('/register/tenant', registerTenant as any);
router.post('/login', login as any);
router.post('/forgot-password', requestPasswordReset as any);
router.post('/reset-password', resetPassword as any);

// Protected routes
router.get('/profile', authenticate as any, getProfile as any);
router.put('/password', authenticate as any, changePassword as any);

export default router;
