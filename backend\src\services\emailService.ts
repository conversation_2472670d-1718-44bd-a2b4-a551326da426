import { Resend } from 'resend';
import config from '../config';
import { TierLevel, getTierById } from '../models/SubscriptionTier';

// Initialize Resend
const resend = new Resend(config.email.resendApiKey);

/**
 * Send a subscription confirmation email
 */
export async function sendSubscriptionConfirmationEmail(
  email: string,
  name: string,
  tierId: TierLevel
): Promise<boolean> {
  try {
    const tier = getTierById(tierId);
    
    if (!tier) {
      throw new Error(`Tier not found: ${tierId}`);
    }
    
    const tierName = tier.name;
    const price = tier.pricing.india;
    const currency = '₹';
    
    const { data, error } = await resend.emails.send({
      from: config.email.from,
      to: email,
      subject: `Your Scheduly ${tierName} Subscription`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #3b82f6;">Subscription Confirmation</h1>
          <p>Hello ${name},</p>
          <p>Thank you for subscribing to Scheduly ${tierName} plan!</p>
          
          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h2 style="margin-top: 0; color: #1f2937;">Subscription Details</h2>
            <p><strong>Plan:</strong> ${tierName}</p>
            <p><strong>Price:</strong> ${price === 0 ? 'Free' : `${currency}${price}/month`}</p>
            ${tier.trialDays > 0 ? `<p><strong>Trial Period:</strong> ${tier.trialDays} days</p>` : ''}
          </div>
          
          <p>You now have access to the following features:</p>
          <ul>
            ${tier.features.map(feature => `<li>${feature}</li>`).join('')}
          </ul>
          
          <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
          
          <p>Best regards,<br>The Scheduly Team</p>
        </div>
      `
    });
    
    if (error) {
      console.error('Error sending subscription confirmation email:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error in sendSubscriptionConfirmationEmail:', error);
    return false;
  }
}

/**
 * Send a subscription updated email
 */
export async function sendSubscriptionUpdatedEmail(
  email: string,
  name: string,
  oldTierId: TierLevel,
  newTierId: TierLevel
): Promise<boolean> {
  try {
    const oldTier = getTierById(oldTierId);
    const newTier = getTierById(newTierId);
    
    if (!oldTier || !newTier) {
      throw new Error(`Tier not found: ${!oldTier ? oldTierId : newTierId}`);
    }
    
    const isUpgrade = oldTierId === TierLevel.FREE || 
                      (oldTierId === TierLevel.PRO && newTierId === TierLevel.PRO_PLUS);
    
    const { data, error } = await resend.emails.send({
      from: config.email.from,
      to: email,
      subject: `Your Scheduly Subscription ${isUpgrade ? 'Upgrade' : 'Change'}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #3b82f6;">Subscription ${isUpgrade ? 'Upgrade' : 'Change'}</h1>
          <p>Hello ${name},</p>
          <p>Your Scheduly subscription has been ${isUpgrade ? 'upgraded' : 'changed'} from ${oldTier.name} to ${newTier.name}.</p>
          
          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h2 style="margin-top: 0; color: #1f2937;">New Subscription Details</h2>
            <p><strong>Plan:</strong> ${newTier.name}</p>
            <p><strong>Price:</strong> ${newTier.pricing.india === 0 ? 'Free' : `₹${newTier.pricing.india}/month`}</p>
            ${newTier.trialDays > 0 ? `<p><strong>Trial Period:</strong> ${newTier.trialDays} days</p>` : ''}
          </div>
          
          <p>You now have access to the following features:</p>
          <ul>
            ${newTier.features.map(feature => `<li>${feature}</li>`).join('')}
          </ul>
          
          <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
          
          <p>Best regards,<br>The Scheduly Team</p>
        </div>
      `
    });
    
    if (error) {
      console.error('Error sending subscription updated email:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error in sendSubscriptionUpdatedEmail:', error);
    return false;
  }
}

/**
 * Send a subscription cancellation email
 */
export async function sendSubscriptionCancellationEmail(
  email: string,
  name: string,
  tierId: TierLevel,
  endDate: Date,
  cancelImmediately: boolean
): Promise<boolean> {
  try {
    const tier = getTierById(tierId);
    
    if (!tier) {
      throw new Error(`Tier not found: ${tierId}`);
    }
    
    const { data, error } = await resend.emails.send({
      from: config.email.from,
      to: email,
      subject: 'Your Scheduly Subscription Cancellation',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #3b82f6;">Subscription Cancellation</h1>
          <p>Hello ${name},</p>
          <p>We're sorry to see you go. Your Scheduly ${tier.name} subscription has been ${cancelImmediately ? 'canceled immediately' : 'scheduled for cancellation'}.</p>
          
          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h2 style="margin-top: 0; color: #1f2937;">Cancellation Details</h2>
            <p><strong>Plan:</strong> ${tier.name}</p>
            ${!cancelImmediately ? `<p><strong>Access Until:</strong> ${endDate.toLocaleDateString()}</p>` : ''}
          </div>
          
          ${!cancelImmediately ? `
            <p>You will continue to have access to all ${tier.name} features until ${endDate.toLocaleDateString()}.</p>
            <p>If you change your mind, you can reactivate your subscription before this date.</p>
          ` : ''}
          
          <p>We'd love to hear your feedback on why you decided to cancel. Your insights help us improve our service.</p>
          
          <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
          
          <p>Best regards,<br>The Scheduly Team</p>
        </div>
      `
    });
    
    if (error) {
      console.error('Error sending subscription cancellation email:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error in sendSubscriptionCancellationEmail:', error);
    return false;
  }
}

/**
 * Send a payment failed email
 */
export async function sendPaymentFailedEmail(
  email: string,
  name: string,
  tierId: TierLevel,
  retryDate: Date
): Promise<boolean> {
  try {
    const tier = getTierById(tierId);
    
    if (!tier) {
      throw new Error(`Tier not found: ${tierId}`);
    }
    
    const { data, error } = await resend.emails.send({
      from: config.email.from,
      to: email,
      subject: 'Action Required: Scheduly Payment Failed',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #ef4444;">Payment Failed</h1>
          <p>Hello ${name},</p>
          <p>We were unable to process your payment for your Scheduly ${tier.name} subscription.</p>
          
          <div style="background-color: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h2 style="margin-top: 0; color: #b91c1c;">Payment Details</h2>
            <p><strong>Plan:</strong> ${tier.name}</p>
            <p><strong>Amount:</strong> ₹${tier.pricing.india}/month</p>
            <p><strong>Next Retry:</strong> ${retryDate.toLocaleDateString()}</p>
          </div>
          
          <p>To ensure uninterrupted access to your subscription, please update your payment method by logging into your account.</p>
          
          <div style="margin: 30px 0; text-align: center;">
            <a href="${config.app.url}/subscription" style="background-color: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold;">Update Payment Method</a>
          </div>
          
          <p>If you have any questions or need assistance, please don't hesitate to contact our support team.</p>
          
          <p>Best regards,<br>The Scheduly Team</p>
        </div>
      `
    });
    
    if (error) {
      console.error('Error sending payment failed email:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error in sendPaymentFailedEmail:', error);
    return false;
  }
}
