#!/usr/bin/env node

/**
 * Fix-all script for Scheduly backend
 * This script helps fix common TypeScript and dependency issues
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 Starting Scheduly Backend Fix-All Script...\n');

// Function to run commands safely
function runCommand(command, description) {
  try {
    console.log(`📋 ${description}...`);
    execSync(command, { stdio: 'inherit', cwd: __dirname });
    console.log(`✅ ${description} completed successfully\n`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    return false;
  }
}

// Function to check if file exists
function fileExists(filePath) {
  return fs.existsSync(path.join(__dirname, filePath));
}

// 1. Check and install dependencies
console.log('1️⃣ Checking dependencies...');
if (!runCommand('npm install', 'Installing dependencies')) {
  console.log('⚠️  Dependency installation failed, continuing...\n');
}

// 2. Check TypeScript compilation
console.log('2️⃣ Checking TypeScript compilation...');
if (!runCommand('npx tsc --noEmit', 'TypeScript type checking')) {
  console.log('⚠️  TypeScript compilation has errors, but continuing...\n');
}

// 3. Check for missing environment files
console.log('3️⃣ Checking environment configuration...');
if (!fileExists('.env')) {
  console.log('⚠️  .env file not found. Please copy .env.example to .env and configure it.');
} else {
  console.log('✅ .env file exists\n');
}

// 4. Check for missing directories
console.log('4️⃣ Checking directory structure...');
const requiredDirs = ['src', 'src/config', 'src/controllers', 'src/middleware', 'src/models', 'src/routes', 'src/utils'];
requiredDirs.forEach(dir => {
  if (!fileExists(dir)) {
    console.log(`⚠️  Directory ${dir} not found`);
  } else {
    console.log(`✅ Directory ${dir} exists`);
  }
});
console.log();

// 5. Try to build the project
console.log('5️⃣ Attempting to build project...');
if (runCommand('npm run build', 'Building project')) {
  console.log('🎉 Build successful!\n');
} else {
  console.log('⚠️  Build failed, but this is normal during development\n');
}

// 6. Check for common issues
console.log('6️⃣ Checking for common issues...');

// Check if dist directory exists after build
if (fileExists('dist')) {
  console.log('✅ dist directory created successfully');
} else {
  console.log('⚠️  dist directory not found - build may have failed');
}

console.log('\n🏁 Fix-all script completed!');
console.log('\n📝 Next steps:');
console.log('   1. Make sure your .env file is properly configured');
console.log('   2. Run "npm run dev" to start the development server');
console.log('   3. Check the console for any remaining errors');
console.log('\n💡 If you still have issues, check the documentation in docs/ folder');
