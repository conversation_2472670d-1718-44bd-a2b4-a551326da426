import twilio from 'twilio';
import { Appointment as AppointmentType, User as UserType, Service as ServiceType, Tenant as TenantType } from '../models/supabase';
import { twilioConfig, isTwilioConfigured } from '../config/integrations';

// Create a Twilio client
const createTwilioClient = () => {
  if (!isTwilioConfigured()) {
    console.warn('Twilio is not configured. Check your environment variables.');
    return null;
  }

  return twilio(twilioConfig.accountSid, twilioConfig.authToken);
};

// Send appointment confirmation SMS
export const sendAppointmentConfirmationSMS = async (
  appointment: AppointmentType,
  client: UserType,
  staff: UserType,
  service: ServiceType,
  tenant: TenantType
) => {
  if (!client.phone) {
    console.log('Client phone number not available');
    return false;
  }

  const twilioClient = createTwilioClient();

  if (!twilioClient) {
    console.warn('Twilio client not available. Skipping SMS send.');
    return false;
  }

  const fromNumber = twilioConfig.phoneNumber;

  if (!fromNumber) {
    console.warn('Twilio phone number not configured');
    return false;
  }

  const startTime = new Date(appointment.startTime);

  const formattedDate = startTime.toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
  });

  const formattedTime = startTime.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
  });

  const message = `
    ${tenant.name}: Your appointment for ${service.name} with ${staff.firstName} ${staff.lastName} is confirmed for ${formattedDate} at ${formattedTime}. Thank you!
  `.trim();

  try {
    const result = await twilioClient.messages.create({
      body: message,
      from: fromNumber,
      to: client.phone,
    });

    console.log('SMS sent:', result.sid);
    return true;
  } catch (error) {
    console.error('Error sending SMS:', error);
    return false;
  }
};

// Send appointment reminder SMS
export const sendAppointmentReminderSMS = async (
  appointment: AppointmentType,
  client: UserType,
  staff: UserType,
  service: ServiceType,
  tenant: TenantType
) => {
  if (!client.phone) {
    console.log('Client phone number not available');
    return false;
  }

  const twilioClient = createTwilioClient();

  if (!twilioClient) {
    console.warn('Twilio client not available. Skipping reminder SMS send.');
    return false;
  }

  const fromNumber = twilioConfig.phoneNumber;

  if (!fromNumber) {
    console.warn('Twilio phone number not configured');
    return false;
  }

  const startTime = new Date(appointment.startTime);

  const formattedDate = startTime.toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
  });

  const formattedTime = startTime.toLocaleTimeString('en-US', {
    hour: '2-digit',
    minute: '2-digit',
  });

  const message = `
    ${tenant.name}: Reminder - Your appointment for ${service.name} with ${staff.firstName} ${staff.lastName} is tomorrow, ${formattedDate} at ${formattedTime}. Reply C to confirm or R to reschedule.
  `.trim();

  try {
    const result = await twilioClient.messages.create({
      body: message,
      from: fromNumber,
      to: client.phone,
    });

    console.log('Reminder SMS sent:', result.sid);
    return true;
  } catch (error) {
    console.error('Error sending reminder SMS:', error);
    return false;
  }
};
