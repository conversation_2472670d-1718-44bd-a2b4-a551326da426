-- Row Level Security Policies for Scheduly

-- Tenant policies
-- Super admins can see all tenants
CREATE POLICY "Super admins can see all tenants" ON tenants
  FOR SELECT USING (is_super_admin());

-- Super admins can insert tenants
CREATE POLICY "Super admins can insert tenants" ON tenants
  FOR INSERT WITH CHECK (is_super_admin());

-- Super admins can update tenants
CREATE POLICY "Super admins can update tenants" ON tenants
  FOR UPDATE USING (is_super_admin()) WITH CHECK (is_super_admin());

-- Super admins can delete tenants
CREATE POLICY "Super admins can delete tenants" ON tenants
  FOR DELETE USING (is_super_admin());

-- Users can see their own tenant
CREATE POLICY "Users can see their own tenant" ON tenants
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users
      WHERE users.tenant_id = tenants.id
      AND users.email = auth.email()
    )
  );

-- User policies
-- Super admins can see all users
CREATE POLICY "Super admins can see all users" ON users
  FOR SELECT USING (is_super_admin());

-- Super admins can insert users
CREATE POLICY "Super admins can insert users" ON users
  FOR INSERT WITH CHECK (is_super_admin());

-- Super admins can update users
CREATE POLICY "Super admins can update users" ON users
  FOR UPDATE USING (is_super_admin()) WITH CHECK (is_super_admin());

-- Super admins can delete users
CREATE POLICY "Super admins can delete users" ON users
  FOR DELETE USING (is_super_admin());

-- Admins can see users in their tenant
CREATE POLICY "Admins can see users in their tenant" ON users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM users AS u
      WHERE u.email = auth.email()
      AND u.role = 'admin'
      AND u.tenant_id = users.tenant_id
    )
  );

-- Admins can insert users in their tenant
CREATE POLICY "Admins can insert users in their tenant" ON users
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM users AS u
      WHERE u.email = auth.email()
      AND u.role = 'admin'
      AND u.tenant_id = users.tenant_id
    )
  );

-- Admins can update users in their tenant
CREATE POLICY "Admins can update users in their tenant" ON users
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM users AS u
      WHERE u.email = auth.email()
      AND u.role = 'admin'
      AND u.tenant_id = users.tenant_id
    )
  ) WITH CHECK (
    EXISTS (
      SELECT 1 FROM users AS u
      WHERE u.email = auth.email()
      AND u.role = 'admin'
      AND u.tenant_id = users.tenant_id
    )
  );

-- Admins can delete users in their tenant
CREATE POLICY "Admins can delete users in their tenant" ON users
  FOR DELETE USING (
    EXISTS (
      SELECT 1 FROM users AS u
      WHERE u.email = auth.email()
      AND u.role = 'admin'
      AND u.tenant_id = users.tenant_id
    )
  );

-- Users can see their own record
CREATE POLICY "Users can see their own record" ON users
  FOR SELECT USING (email = auth.email());

-- Users can update their own record (except role and tenant_id)
CREATE POLICY "Users can update their own record" ON users
  FOR UPDATE USING (email = auth.email())
  WITH CHECK (email = auth.email());

-- Service policies
-- Similar policies for services, staff_services, availabilities, etc.
-- These would follow the same pattern as above, with appropriate tenant and role checks

-- Create a trigger to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
   NEW.updated_at = NOW();
   RETURN NEW;
END;
$$ language 'plpgsql';

-- Create a trigger to prevent users from changing their role or tenant_id
CREATE OR REPLACE FUNCTION prevent_role_tenant_change()
RETURNS TRIGGER AS $$
BEGIN
   IF OLD.role != NEW.role THEN
      RAISE EXCEPTION 'Cannot change user role';
   END IF;

   IF OLD.tenant_id != NEW.tenant_id THEN
      RAISE EXCEPTION 'Cannot change user tenant';
   END IF;

   RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply the trigger to all tables
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_services_updated_at BEFORE UPDATE ON services FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_staff_services_updated_at BEFORE UPDATE ON staff_services FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_availabilities_updated_at BEFORE UPDATE ON availabilities FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_time_offs_updated_at BEFORE UPDATE ON time_offs FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_appointments_updated_at BEFORE UPDATE ON appointments FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();
CREATE TRIGGER update_notifications_updated_at BEFORE UPDATE ON notifications FOR EACH ROW EXECUTE PROCEDURE update_updated_at_column();

-- Apply the role/tenant protection trigger to users table
CREATE TRIGGER prevent_role_tenant_change_trigger BEFORE UPDATE ON users FOR EACH ROW EXECUTE PROCEDURE prevent_role_tenant_change();
