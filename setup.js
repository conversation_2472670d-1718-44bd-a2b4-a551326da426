#!/usr/bin/env node

/**
 * Scheduly Project Setup Script
 * This script helps set up the entire Scheduly project
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Scheduly Project Setup...\n');

// Function to run commands safely
function runCommand(command, description, cwd = process.cwd()) {
  try {
    console.log(`📋 ${description}...`);
    execSync(command, { stdio: 'inherit', cwd });
    console.log(`✅ ${description} completed successfully\n`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    return false;
  }
}

// Function to check if file exists
function fileExists(filePath) {
  return fs.existsSync(filePath);
}

// 1. Install root dependencies
console.log('1️⃣ Installing root dependencies...');
runCommand('npm install', 'Installing root workspace dependencies');

// 2. Install backend dependencies
console.log('2️⃣ Setting up backend...');
if (fileExists('./backend')) {
  runCommand('npm install', 'Installing backend dependencies', './backend');
  
  // Check if .env exists in backend
  if (!fileExists('./backend/.env')) {
    console.log('⚠️  Backend .env file not found. Please copy backend/.env.example to backend/.env and configure it.');
  } else {
    console.log('✅ Backend .env file exists');
  }
} else {
  console.log('❌ Backend directory not found');
}

// 3. Install frontend dependencies
console.log('3️⃣ Setting up frontend...');
if (fileExists('./frontend')) {
  runCommand('npm install', 'Installing frontend dependencies', './frontend');
  
  // Check if .env exists in frontend
  if (!fileExists('./frontend/.env')) {
    console.log('⚠️  Frontend .env file not found. Please copy frontend/.env.example to frontend/.env and configure it.');
  } else {
    console.log('✅ Frontend .env file exists');
  }
} else {
  console.log('❌ Frontend directory not found');
}

// 4. Check database setup
console.log('4️⃣ Checking database setup...');
console.log('📝 Database setup checklist:');
console.log('   □ Create a Supabase project');
console.log('   □ Run the migration scripts in database/migrations/');
console.log('   □ Configure RLS policies');
console.log('   □ Update environment variables with Supabase credentials');

// 5. Check external services
console.log('5️⃣ Checking external services configuration...');
console.log('📝 External services checklist:');
console.log('   □ Configure Resend for email notifications');
console.log('   □ Configure Twilio for SMS notifications');
console.log('   □ Configure Google OAuth for calendar integration');
console.log('   □ Configure Razorpay for payments');

// 6. Try to build both projects
console.log('6️⃣ Testing builds...');
if (fileExists('./backend')) {
  runCommand('npm run build', 'Building backend', './backend');
}

if (fileExists('./frontend')) {
  runCommand('npm run build', 'Building frontend', './frontend');
}

console.log('\n🎉 Setup completed!');
console.log('\n📝 Next steps:');
console.log('   1. Configure your environment variables in backend/.env and frontend/.env');
console.log('   2. Set up your Supabase database using the migration scripts');
console.log('   3. Configure external services (Resend, Twilio, Google OAuth, Razorpay)');
console.log('   4. Run "npm run dev" to start both backend and frontend servers');
console.log('\n🔧 Available commands:');
console.log('   npm run dev              - Start both backend and frontend');
console.log('   npm run dev:backend      - Start only backend');
console.log('   npm run dev:frontend     - Start only frontend');
console.log('   npm run build            - Build both projects');
console.log('   npm run test             - Run tests for both projects');
console.log('\n📚 Documentation:');
console.log('   Check the docs/ folder for detailed setup instructions');
console.log('   README.md contains project overview and quick start guide');
