import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import Layout from '../components/Layout';
import { useAuth } from '../contexts/AuthContext';
import { superAdminService } from '../services';
import {
  FiUsers,
  FiGrid,
  FiCalendar,
  FiArrowRight,
  FiCheckCircle,
  FiAlertTriangle,
  FiCreditCard
} from 'react-icons/fi';

interface DashboardStats {
  totalTenants: number;
  activeTenants: number;
  totalUsers: number;
  totalAppointments: number;
}

interface RecentTenant {
  id: string;
  name: string;
  email: string;
  createdAt: string;
  active: boolean;
}

const SuperAdminDashboard: React.FC = () => {
  const { user } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    totalTenants: 0,
    activeTenants: 0,
    totalUsers: 0,
    totalAppointments: 0
  });
  const [recentTenants, setRecentTenants] = useState<RecentTenant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const data = await superAdminService.getDashboardData();

        setStats(data.stats);
        setRecentTenants(data.recentTenants);
      } catch (err: any) {
        setError('Failed to load dashboard data. Please try again later.');
        console.error('Error fetching dashboard data:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Super Admin Dashboard</h1>
          <p className="text-gray-600">Monitor and manage all business accounts from here.</p>
        </div>

        {error && (
          <div className="bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-6" role="alert">
            <div className="flex items-center">
              <FiAlertTriangle className="mr-2" />
              <p>{error}</p>
            </div>
          </div>
        )}

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-100">
            <div className="flex items-center mb-4">
              <div className="p-3 rounded-lg bg-blue-100 mr-3">
                <FiGrid className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-700">Total Businesses</h3>
            </div>
            <p className="text-3xl font-bold text-gray-800">{stats.totalTenants}</p>
            <div className="mt-2 text-sm text-gray-500">
              <span className="text-green-500 font-medium">{stats.activeTenants} active</span>
              {' / '}
              <span className="text-red-500 font-medium">{stats.totalTenants - stats.activeTenants} inactive</span>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-100">
            <div className="flex items-center mb-4">
              <div className="p-3 rounded-lg bg-purple-100 mr-3">
                <FiUsers className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-700">Total Users</h3>
            </div>
            <p className="text-3xl font-bold text-gray-800">{stats.totalUsers}</p>
            <div className="mt-2 text-sm text-gray-500">
              Across all businesses
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-100">
            <div className="flex items-center mb-4">
              <div className="p-3 rounded-lg bg-green-100 mr-3">
                <FiCalendar className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-700">Total Appointments</h3>
            </div>
            <p className="text-3xl font-bold text-gray-800">{stats.totalAppointments}</p>
            <div className="mt-2 text-sm text-gray-500">
              Across all businesses
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6 border border-gray-100">
            <div className="flex items-center mb-4">
              <div className="p-3 rounded-lg bg-blue-100 mr-3">
                <FiCheckCircle className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-700">Active Rate</h3>
            </div>
            <p className="text-3xl font-bold text-gray-800">
              {stats.totalTenants > 0
                ? Math.round((stats.activeTenants / stats.totalTenants) * 100)
                : 0}%
            </p>
            <div className="mt-2 text-sm text-gray-500">
              Of businesses are active
            </div>
          </div>
        </div>

        {/* Admin Tools */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* Subscription Management */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100">
            <div className="px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-purple-50 to-pink-50">
              <div className="flex items-center">
                <FiCreditCard className="h-5 w-5 text-purple-600 mr-2" />
                <h2 className="text-xl font-bold text-gray-800">Subscription Management</h2>
              </div>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-4">
                Manage user subscriptions, update tiers, and handle billing issues.
              </p>
              <div className="flex space-x-4">
                <Link
                  to="/admin/subscription"
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-purple-600 hover:bg-purple-700"
                >
                  Manage Subscriptions
                </Link>
                <Link
                  to="/admin/subscription/analytics"
                  className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50"
                >
                  View Analytics
                </Link>
              </div>
            </div>
          </div>

          {/* Business Management */}
          <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100">
            <div className="px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
              <div className="flex items-center">
                <FiUsers className="h-5 w-5 text-blue-600 mr-2" />
                <h2 className="text-xl font-bold text-gray-800">Business Management</h2>
              </div>
            </div>
            <div className="p-6">
              <p className="text-gray-600 mb-4">
                View and manage all businesses registered on the platform.
              </p>
              <Link
                to="/super-admin/businesses"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
              >
                Manage Businesses
              </Link>
            </div>
          </div>
        </div>

        {/* Recent Businesses */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100 mb-8">
          <div className="px-6 py-4 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <FiGrid className="h-5 w-5 text-blue-600 mr-2" />
                <h2 className="text-xl font-bold text-gray-800">Recently Added Businesses</h2>
              </div>
              <Link to="/super-admin/businesses" className="text-blue-600 hover:text-blue-800 flex items-center text-sm font-medium">
                View All <FiArrowRight className="ml-1" />
              </Link>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Business Name
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Email
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentTenants.length > 0 ? (
                  recentTenants.map((tenant) => (
                    <tr key={tenant.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{tenant.name}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">{tenant.email}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-500">
                          {new Date(tenant.createdAt).toLocaleDateString()}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {tenant.active ? (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            Active
                          </span>
                        ) : (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                            Inactive
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <Link to={`/super-admin/businesses/${tenant.id}`} className="text-blue-600 hover:text-blue-900 mr-4">
                          View
                        </Link>
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                      No businesses found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default SuperAdminDashboard;
