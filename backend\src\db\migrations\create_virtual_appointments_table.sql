-- Create enum for virtual appointment status
CREATE TYPE virtual_appointment_status AS ENUM (
  'scheduled',
  'in_progress',
  'completed',
  'cancelled'
);

-- Create virtual appointments table
CREATE TABLE IF NOT EXISTS virtual_appointments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  appointment_id UUID NOT NULL REFERENCES appointments(id) ON DELETE CASCADE,
  meeting_id UUID NOT NULL,
  host_url TEXT NOT NULL,
  guest_url TEXT NOT NULL,
  status virtual_appointment_status NOT NULL DEFAULT 'scheduled',
  recording_url TEXT,
  created_by UUID NOT NULL REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_by UUID REFERENCES users(id),
  metadata JSONB
);

-- Create index on appointment_id
CREATE INDEX idx_virtual_appointments_appointment_id ON virtual_appointments(appointment_id);

-- Create index on meeting_id
CREATE INDEX idx_virtual_appointments_meeting_id ON virtual_appointments(meeting_id);

-- Add virtual appointment fields to appointments table
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS is_virtual BOOLEAN DEFAULT FALSE;
ALTER TABLE appointments ADD COLUMN IF NOT EXISTS virtual_appointment_id UUID REFERENCES virtual_appointments(id);

-- Create RLS policies
ALTER TABLE virtual_appointments ENABLE ROW LEVEL SECURITY;

-- Policy for users to view virtual appointments they're involved in
CREATE POLICY virtual_appointments_select_policy ON virtual_appointments
  FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM appointments a
      WHERE a.id = appointment_id
      AND (
        a.client_id = auth.uid() OR
        a.staff_id = auth.uid() OR
        EXISTS (
          SELECT 1 FROM tenants t
          WHERE t.id = a.tenant_id
          AND t.owner_id = auth.uid()
        )
      )
    )
  );

-- Policy for users to insert virtual appointments for their tenants
CREATE POLICY virtual_appointments_insert_policy ON virtual_appointments
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM appointments a
      JOIN tenants t ON a.tenant_id = t.id
      WHERE a.id = appointment_id
      AND t.owner_id = auth.uid()
    )
  );

-- Policy for users to update virtual appointments they're involved in
CREATE POLICY virtual_appointments_update_policy ON virtual_appointments
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM appointments a
      WHERE a.id = appointment_id
      AND (
        a.staff_id = auth.uid() OR
        EXISTS (
          SELECT 1 FROM tenants t
          WHERE t.id = a.tenant_id
          AND t.owner_id = auth.uid()
        )
      )
    )
  );
