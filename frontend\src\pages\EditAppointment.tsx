import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import Layout from '../components/Layout';
import { useAuth } from '../contexts/AuthContext';
import { useCurrency } from '../contexts/CurrencyContext';
import { useTimezone } from '../contexts/TimezoneContext';
import {
  appointmentService,
  userService,
  serviceService,
  AppointmentData,
  UserRole,
  AppointmentStatus
} from '../services';
import { FiCalendar, FiClock, FiUser, FiUsers, FiPackage, FiFileText } from 'react-icons/fi';

interface Client {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
}

interface Staff {
  id: string;
  firstName: string;
  lastName: string;
}

interface Service {
  id: string;
  name: string;
  duration: number;
  price: number;
}

interface TimeSlot {
  startTime: string;
  endTime: string;
}

const EditAppointment: React.FC = () => {
  const { tenant, user } = useAuth();
  const { formatPrice } = useCurrency();
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Form state
  const [selectedClient, setSelectedClient] = useState<string>('');
  const [selectedService, setSelectedService] = useState<string>('');
  const [selectedStaff, setSelectedStaff] = useState<string>('');
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [selectedTime, setSelectedTime] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [status, setStatus] = useState<AppointmentStatus>(AppointmentStatus.CONFIRMED);

  // Data state
  const [clients, setClients] = useState<Client[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [staff, setStaff] = useState<Staff[]>([]);
  const [availableTimeSlots, setAvailableTimeSlots] = useState<string[]>([]);
  const [originalAppointment, setOriginalAppointment] = useState<any>(null);

  // UI state
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeSlotLoading, setTimeSlotLoading] = useState(false);

  // Get today's date in YYYY-MM-DD format for the date input min value
  const today = new Date().toISOString().split('T')[0];

  // Load appointment data
  useEffect(() => {
    const fetchAppointment = async () => {
      if (!tenant || !id) return;

      try {
        const appointmentData = await appointmentService.getAppointmentById(tenant.id, id);
        setOriginalAppointment(appointmentData);

        // Extract date and time from startTime
        const startDateTime = new Date(appointmentData.startTime);
        const dateStr = startDateTime.toISOString().split('T')[0];
        const timeStr = startDateTime.toISOString().split('T')[1].substring(0, 8); // HH:MM:SS format

        // Set form values
        setSelectedClient(appointmentData.client.id);
        setSelectedService(appointmentData.service.id);
        setSelectedStaff(appointmentData.staff.id);
        setSelectedDate(dateStr);
        setSelectedTime(timeStr);
        setNotes(appointmentData.notes || '');
        setStatus(appointmentData.status);
      } catch (err: any) {
        console.error('Error fetching appointment:', err);
        setError('Failed to load appointment details. Please try again later.');
      }
    };

    fetchAppointment();
  }, [tenant, id]);

  // Load clients, services, and staff
  useEffect(() => {
    const fetchData = async () => {
      if (!tenant) return;

      setIsLoading(true);
      setError(null);

      try {
        // Fetch clients
        const clientsData = await userService.getUsers(tenant.id, UserRole.CLIENT);
        setClients(clientsData);

        // Fetch services
        const servicesData = await serviceService.getServices(tenant.id);
        setServices(servicesData);

        // Fetch staff
        const staffData = await userService.getUsers(tenant.id, UserRole.STAFF);
        setStaff(staffData);
      } catch (err: any) {
        console.error('Error fetching data:', err);
        setError('Failed to load required data. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [tenant]);

  // Fetch available time slots when service, staff, and date are selected
  useEffect(() => {
    const fetchTimeSlots = async () => {
      if (!tenant || !selectedService || !selectedStaff || !selectedDate || !originalAppointment) return;

      setTimeSlotLoading(true);

      try {
        const timeSlotsData = await appointmentService.getAvailableTimeSlots(
          tenant.id,
          {
            serviceId: selectedService,
            staffId: selectedStaff,
            date: selectedDate,
            excludeAppointmentId: id // Exclude current appointment from availability check
          }
        );

        // Extract just the start times from the time slots
        const startTimes = timeSlotsData.timeSlots.map(slot => slot.startTime.split('T')[1]);
        
        // Add the current time slot if it's not in the list
        if (selectedTime && !startTimes.includes(selectedTime)) {
          startTimes.push(selectedTime);
          startTimes.sort();
        }
        
        setAvailableTimeSlots(startTimes);
      } catch (err: any) {
        console.error('Error fetching time slots:', err);
        setError('Failed to load available time slots. Please try again.');
      } finally {
        setTimeSlotLoading(false);
      }
    };

    if (originalAppointment) {
      fetchTimeSlots();
    }
  }, [tenant, selectedService, selectedStaff, selectedDate, originalAppointment, id]);

  // Format time for display (e.g., "14:30:00" to "2:30 PM")
  const formatTime = (time: string): string => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours, 10);
    const minute = minutes;
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const formattedHour = hour % 12 || 12;
    return `${formattedHour}:${minute} ${ampm}`;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    if (!selectedClient || !selectedService || !selectedStaff || !selectedDate || !selectedTime) {
      setError('Please fill in all required fields');
      return;
    }

    if (!tenant || !id) {
      setError('Tenant or appointment information is missing');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // Create appointment data
      const appointmentData: Partial<AppointmentData> = {
        clientId: selectedClient,
        staffId: selectedStaff,
        serviceId: selectedService,
        startTime: `${selectedDate}T${selectedTime}`,
        notes: notes,
        status: status
      };

      // Update appointment
      await appointmentService.updateAppointment(tenant.id, id, appointmentData);

      // Navigate to appointment details
      navigate(`/appointments/${id}`);
    } catch (err: any) {
      console.error('Error updating appointment:', err);
      setError(err.response?.data?.message || 'Failed to update appointment. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Layout>
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800">Edit Appointment</h1>
          <p className="text-gray-600 mt-2">
            Update the appointment details
          </p>
        </div>

        {error && (
          <div className="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg">
            {error}
          </div>
        )}

        {isLoading ? (
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-4 border-b-4 border-blue-600"></div>
          </div>
        ) : (
          <div className="bg-white rounded-2xl shadow-md p-8 border border-gray-100">
            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                {/* Client Selection */}
                <div>
                  <label htmlFor="client" className="block text-gray-700 font-medium mb-2 flex items-center">
                    <FiUser className="mr-2 text-blue-600" />
                    Client *
                  </label>
                  <select
                    id="client"
                    value={selectedClient}
                    onChange={(e) => setSelectedClient(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select a client</option>
                    {clients.map((client) => (
                      <option key={client.id} value={client.id}>
                        {client.firstName} {client.lastName} ({client.email})
                      </option>
                    ))}
                  </select>
                </div>

                {/* Service Selection */}
                <div>
                  <label htmlFor="service" className="block text-gray-700 font-medium mb-2 flex items-center">
                    <FiPackage className="mr-2 text-blue-600" />
                    Service *
                  </label>
                  <select
                    id="service"
                    value={selectedService}
                    onChange={(e) => setSelectedService(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Select a service</option>
                    {services.map((service) => (
                      <option key={service.id} value={service.id}>
                        {service.name} ({service.duration} min, {formatPrice(service.price)})
                      </option>
                    ))}
                  </select>
                </div>

                {/* Staff Selection */}
                <div>
                  <label htmlFor="staff" className="block text-gray-700 font-medium mb-2 flex items-center">
                    <FiUsers className="mr-2 text-blue-600" />
                    Staff Member *
                  </label>
                  <select
                    id="staff"
                    value={selectedStaff}
                    onChange={(e) => setSelectedStaff(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                    disabled={user?.role === UserRole.STAFF}
                  >
                    <option value="">Select a staff member</option>
                    {staff.map((staffMember) => (
                      <option key={staffMember.id} value={staffMember.id}>
                        {staffMember.firstName} {staffMember.lastName}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Date Selection */}
                <div>
                  <label htmlFor="date" className="block text-gray-700 font-medium mb-2 flex items-center">
                    <FiCalendar className="mr-2 text-blue-600" />
                    Date *
                  </label>
                  <input
                    type="date"
                    id="date"
                    value={selectedDate}
                    onChange={(e) => setSelectedDate(e.target.value)}
                    min={today}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                </div>
              </div>

              {/* Status Selection */}
              <div className="mb-6">
                <label htmlFor="status" className="block text-gray-700 font-medium mb-2 flex items-center">
                  <FiClock className="mr-2 text-blue-600" />
                  Status *
                </label>
                <select
                  id="status"
                  value={status}
                  onChange={(e) => setStatus(e.target.value as AppointmentStatus)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value={AppointmentStatus.PENDING}>Pending</option>
                  <option value={AppointmentStatus.CONFIRMED}>Confirmed</option>
                  <option value={AppointmentStatus.COMPLETED}>Completed</option>
                  <option value={AppointmentStatus.CANCELLED}>Cancelled</option>
                  <option value={AppointmentStatus.NO_SHOW}>No Show</option>
                </select>
              </div>

              {/* Time Slots */}
              {selectedService && selectedStaff && selectedDate && (
                <div className="mb-6">
                  <label className="block text-gray-700 font-medium mb-2 flex items-center">
                    <FiClock className="mr-2 text-blue-600" />
                    Available Time Slots *
                  </label>

                  {timeSlotLoading ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
                    </div>
                  ) : availableTimeSlots.length > 0 ? (
                    <div className="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-2">
                      {availableTimeSlots.map((time) => (
                        <button
                          key={time}
                          type="button"
                          className={`py-2 px-3 rounded-lg text-center transition-colors ${
                            selectedTime === time
                              ? 'bg-blue-600 text-white'
                              : 'bg-gray-100 hover:bg-gray-200 text-gray-800'
                          }`}
                          onClick={() => setSelectedTime(time)}
                        >
                          {formatTime(time)}
                        </button>
                      ))}
                    </div>
                  ) : (
                    <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 p-4 rounded-lg">
                      <p>No available time slots for the selected date. Please try another date or staff member.</p>
                    </div>
                  )}
                </div>
              )}

              {/* Notes */}
              <div className="mb-6">
                <label htmlFor="notes" className="block text-gray-700 font-medium mb-2 flex items-center">
                  <FiFileText className="mr-2 text-blue-600" />
                  Notes (Optional)
                </label>
                <textarea
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Add any additional notes or special requests"
                ></textarea>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={isSubmitting || !selectedClient || !selectedService || !selectedStaff || !selectedDate || !selectedTime}
                  className="bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                      Updating...
                    </>
                  ) : (
                    'Update Appointment'
                  )}
                </button>
              </div>
            </form>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default EditAppointment;
