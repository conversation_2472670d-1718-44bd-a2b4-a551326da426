const axios = require('axios');

const BASE_URL = 'http://localhost:5000/api';

async function testSuperAdminFlow() {
  try {
    console.log('🧪 Testing Super Admin API Flow...\n');

    // Step 1: Login as super admin
    console.log('1. Logging in as super admin...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'SuperAdmin123!'
    });

    if (loginResponse.status === 200 && loginResponse.data.token) {
      console.log('✅ Login successful');
      console.log('   Token received:', loginResponse.data.token.substring(0, 20) + '...');
      console.log('   User role:', loginResponse.data.user.role);
    } else {
      console.log('❌ Login failed');
      return;
    }

    const token = loginResponse.data.token;
    const headers = { Authorization: `Bearer ${token}` };

    // Step 2: Test profile endpoint
    console.log('\n2. Testing profile endpoint...');
    try {
      const profileResponse = await axios.get(`${BASE_URL}/auth/profile`, { headers });
      console.log('✅ Profile endpoint working');
      console.log('   User:', profileResponse.data.data.user.email);
      console.log('   Role:', profileResponse.data.data.user.role);
    } catch (error) {
      console.log('❌ Profile endpoint failed:', error.response?.data?.message || error.message);
    }

    // Step 3: Test super admin dashboard
    console.log('\n3. Testing super admin dashboard...');
    try {
      const dashboardResponse = await axios.get(`${BASE_URL}/super-admin/dashboard`, { headers });
      console.log('✅ Dashboard endpoint working');
      console.log('   Stats:', dashboardResponse.data.data.stats);
      console.log('   Recent tenants:', dashboardResponse.data.data.recentTenants.length);
    } catch (error) {
      console.log('❌ Dashboard endpoint failed:', error.response?.data?.message || error.message);
    }

    // Step 4: Test tenants endpoint
    console.log('\n4. Testing tenants endpoint...');
    try {
      const tenantsResponse = await axios.get(`${BASE_URL}/super-admin/tenants`, { headers });
      console.log('✅ Tenants endpoint working');
      console.log('   Total tenants:', tenantsResponse.data.data.length);
    } catch (error) {
      console.log('❌ Tenants endpoint failed:', error.response?.data?.message || error.message);
    }

    // Step 5: Test subscription endpoints
    console.log('\n5. Testing subscription endpoints...');
    try {
      const subscriptionsResponse = await axios.get(`${BASE_URL}/admin/subscription/subscriptions`, { headers });
      console.log('✅ Subscriptions endpoint working');
      console.log('   Total subscriptions:', subscriptionsResponse.data.data.subscriptions.length);
    } catch (error) {
      console.log('❌ Subscriptions endpoint failed:', error.response?.data?.message || error.message);
    }

    try {
      const analyticsResponse = await axios.get(`${BASE_URL}/admin/subscription/analytics`, { headers });
      console.log('✅ Analytics endpoint working');
      console.log('   Tier counts:', analyticsResponse.data.data.tierCounts.length);
    } catch (error) {
      console.log('❌ Analytics endpoint failed:', error.response?.data?.message || error.message);
    }

    console.log('\n🎉 API testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.response?.data?.message || error.message);
  }
}

testSuperAdminFlow();
