{"name": "scheduly", "version": "1.0.0", "description": "Scheduly - Online Appointment Scheduler", "private": true, "workspaces": ["backend", "frontend"], "scripts": {"dev": "concurrently \"npm run dev --workspace=backend\" \"npm run dev --workspace=frontend\"", "dev:backend": "npm run dev --workspace=backend", "dev:frontend": "npm run dev --workspace=frontend", "build": "npm run build --workspace=backend && npm run build --workspace=frontend", "build:backend": "npm run build --workspace=backend", "build:frontend": "npm run build --workspace=frontend", "test": "npm run test --workspace=backend && npm run test --workspace=frontend", "lint": "npm run lint --workspace=backend && npm run lint --workspace=frontend", "install:all": "npm install && npm install --workspace=backend && npm install --workspace=frontend"}, "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}