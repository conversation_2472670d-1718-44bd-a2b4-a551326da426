import { createClient } from '@supabase/supabase-js';
import config from '../config';
import { userHasFeature } from './subscriptionService';

// Initialize Supabase client
const supabase = createClient(config.supabase.url, config.supabase.serviceKey);

// Check if user can use advanced client management features
export async function canUseAdvancedClientFeatures(userId: string): Promise<{
  canUse: boolean;
  reason?: string;
}> {
  try {
    const hasFeature = await userHasFeature(userId, 'advancedClientManagement');
    
    if (!hasFeature) {
      return {
        canUse: false,
        reason: 'Advanced client management requires a Pro or Pro+ subscription'
      };
    }
    
    return { canUse: true };
  } catch (error) {
    console.error('Error in canUseAdvancedClientFeatures:', error);
    return {
      canUse: false,
      reason: 'Error checking feature access'
    };
  }
}

// Get client by ID
export async function getClientById(clientId: string) {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', clientId)
      .eq('role', 'client')
      .single();
    
    if (error) {
      console.error('Error fetching client:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error in getClientById:', error);
    return null;
  }
}

// Get clients for a tenant with tier-based features
export async function getClients(tenantId: string, userId: string, options: any = {}) {
  try {
    // Check if user can use advanced client management features
    const canUseAdvanced = await canUseAdvancedClientFeatures(userId);
    const isAdvanced = canUseAdvanced.canUse;
    
    // Build query
    let query = supabase
      .from('users')
      .select(
        isAdvanced 
          ? `*, client_metadata, appointments:appointments(*)` 
          : `id, first_name, last_name, email, phone`
      )
      .eq('tenant_id', tenantId)
      .eq('role', 'client');
    
    // Apply filters (only available with advanced client management)
    if (isAdvanced) {
      if (options.lastVisit) {
        const date = new Date(options.lastVisit);
        query = query.gte('last_appointment_date', date.toISOString());
      }
      
      if (options.tags) {
        query = query.contains('tags', options.tags);
      }
      
      if (options.customFields) {
        for (const [key, value] of Object.entries(options.customFields)) {
          query = query.contains('client_metadata', { [key]: value });
        }
      }
    }
    
    // Execute query
    const { data, error } = await query;
    
    if (error) {
      console.error('Error fetching clients:', error);
      return [];
    }
    
    return data;
  } catch (error) {
    console.error('Error in getClients:', error);
    return [];
  }
}

// Create a new client
export async function createClient(clientData: any) {
  try {
    const { data, error } = await supabase
      .from('users')
      .insert(clientData)
      .select()
      .single();
    
    if (error) {
      console.error('Error creating client:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error in createClient:', error);
    return null;
  }
}

// Update a client with tier-based features
export async function updateClient(clientId: string, updateData: any, userId: string) {
  try {
    // Check if user is trying to update advanced fields
    const hasAdvancedFields = updateData.client_metadata || 
                             updateData.tags || 
                             updateData.custom_fields;
    
    if (hasAdvancedFields) {
      // Check if user can use advanced client management features
      const canUseAdvanced = await canUseAdvancedClientFeatures(userId);
      
      if (!canUseAdvanced.canUse) {
        return {
          success: false,
          message: canUseAdvanced.reason,
          upgradeRequired: true,
          requiredFeature: 'advancedClientManagement'
        };
      }
    }
    
    // Update client
    const { data, error } = await supabase
      .from('users')
      .update(updateData)
      .eq('id', clientId)
      .eq('role', 'client')
      .select()
      .single();
    
    if (error) {
      console.error('Error updating client:', error);
      return {
        success: false,
        message: 'Failed to update client'
      };
    }
    
    return {
      success: true,
      data
    };
  } catch (error) {
    console.error('Error in updateClient:', error);
    return {
      success: false,
      message: 'Internal server error'
    };
  }
}

// Delete a client
export async function deleteClient(clientId: string) {
  try {
    const { error } = await supabase
      .from('users')
      .delete()
      .eq('id', clientId)
      .eq('role', 'client');
    
    if (error) {
      console.error('Error deleting client:', error);
      return false;
    }
    
    return true;
  } catch (error) {
    console.error('Error in deleteClient:', error);
    return false;
  }
}
