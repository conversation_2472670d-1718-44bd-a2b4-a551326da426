import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { googleCalendarService } from '../services';

const GoogleCalendarIntegration: React.FC = () => {
  const { tenant, user } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [isTokenExpired, setIsTokenExpired] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    const checkConnectionStatus = async () => {
      if (!tenant || !user) return;

      try {
        setIsLoading(true);
        setError(null);

        const status = await googleCalendarService.getConnectionStatus(tenant.id);

        setIsConnected(status.isConnected);
        setIsTokenExpired(status.isTokenExpired);
      } catch (err: any) {
        console.error('Error checking Google Calendar connection status:', err);
        setError('Failed to check Google Calendar connection status');
      } finally {
        setIsLoading(false);
      }
    };

    checkConnectionStatus();
  }, [tenant, user]);

  const handleConnect = async () => {
    if (!tenant) return;

    try {
      setIsLoading(true);
      setError(null);
      setSuccess(null);

      const response = await googleCalendarService.getAuthUrl(tenant.id);

      if (!response.authUrl) {
        throw new Error('Google OAuth is not configured properly. Please check the server configuration.');
      }

      // Open the auth URL in a new window
      const authWindow = window.open(response.authUrl, 'Google Calendar Authorization', 'width=600,height=700');

      if (!authWindow) {
        throw new Error('Pop-up window was blocked. Please allow pop-ups for this site and try again.');
      }

      // Poll for the auth code
      const pollForCode = setInterval(() => {
        try {
          if (authWindow && authWindow.closed) {
            clearInterval(pollForCode);
            setIsLoading(false);
            return;
          }

          if (authWindow && authWindow.location.href.includes('code=')) {
            // Extract the code from the URL
            const code = new URL(authWindow.location.href).searchParams.get('code');

            if (code) {
              authWindow.close();
              clearInterval(pollForCode);

              // Exchange the code for tokens
              handleOAuthCallback(code);
            }
          }
        } catch (e) {
          // Ignore cross-origin errors while polling
        }
      }, 500);
    } catch (err: any) {
      console.error('Error connecting to Google Calendar:', err);

      if (err.response?.data?.message?.includes('OAuth client was not found')) {
        setError('Google OAuth client is not configured. Please see the documentation for setup instructions.');
      } else if (err.message) {
        setError(err.message);
      } else {
        setError('Failed to connect to Google Calendar. Please check the server logs for more information.');
      }

      setIsLoading(false);
    }
  };

  const handleOAuthCallback = async (code: string) => {
    if (!tenant) return;

    try {
      await googleCalendarService.handleOAuthCallback(tenant.id, code);

      setIsConnected(true);
      setIsTokenExpired(false);
      setSuccess('Google Calendar connected successfully!');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err: any) {
      console.error('Error handling OAuth callback:', err);

      if (err.response?.data?.message?.includes('invalid_client')) {
        setError('Google OAuth client is invalid. Please check your Google Cloud Console configuration.');
      } else if (err.response?.data?.message?.includes('invalid_grant')) {
        setError('Authorization code is invalid or expired. Please try connecting again.');
      } else if (err.message) {
        setError(err.message);
      } else {
        setError('Failed to connect to Google Calendar. Please check the server logs for more information.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleDisconnect = async () => {
    if (!tenant) return;

    try {
      setIsLoading(true);
      setError(null);
      setSuccess(null);

      await googleCalendarService.disconnectGoogleCalendar(tenant.id);

      setIsConnected(false);
      setSuccess('Google Calendar disconnected successfully!');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess(null);
      }, 3000);
    } catch (err: any) {
      console.error('Error disconnecting from Google Calendar:', err);
      setError('Failed to disconnect from Google Calendar');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold mb-4">Google Calendar Integration</h2>
        <div className="flex justify-center py-4">
          <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold mb-4">Google Calendar Integration</h2>

      {error && (
        <div className="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p>{error}</p>
          {error.includes('Google OAuth client') && (
            <p className="mt-2 text-sm">
              Please follow the setup instructions in the{' '}
              <a
                href="http://localhost:5000/docs/google-calendar-setup.md"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 underline hover:text-blue-800"
              >
                Google Calendar Integration Guide
              </a>
            </p>
          )}
        </div>
      )}

      {success && (
        <div className="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          {success}
        </div>
      )}

      <div className="mb-4">
        <p className="text-gray-700">
          Connect your Google Calendar to automatically sync your appointments. This allows you to:
        </p>
        <ul className="list-disc list-inside mt-2 text-gray-600">
          <li>See your appointments in your Google Calendar</li>
          <li>Avoid double-booking by syncing your availability</li>
          <li>Get appointment reminders through Google Calendar</li>
        </ul>
      </div>

      <div className="flex items-center">
        <div className="flex-1">
          <div className="flex items-center">
            <div className={`h-3 w-3 rounded-full mr-2 ${isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
            <span className="font-medium">
              {isConnected ? 'Connected' : 'Not Connected'}
            </span>
          </div>
          {isConnected && isTokenExpired && (
            <p className="text-sm text-red-600 mt-1">
              Your connection has expired. Please reconnect.
            </p>
          )}
        </div>

        {isConnected ? (
          <button
            onClick={handleDisconnect}
            className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
            disabled={isLoading}
          >
            Disconnect
          </button>
        ) : (
          <button
            onClick={handleConnect}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            disabled={isLoading}
          >
            Connect
          </button>
        )}
      </div>
    </div>
  );
};

export default GoogleCalendarIntegration;
