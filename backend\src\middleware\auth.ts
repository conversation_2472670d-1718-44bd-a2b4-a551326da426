import { Request, Response, NextFunction, RequestHandler } from 'express';
import { verifyToken } from '../utils/jwt';
import { UserRole } from '../models/supabase';

// Extend Express Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        tenantId: string | null;
        role: UserRole;
      };
    }
  }
}

// Authentication middleware implementation
const authenticateImpl = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      res.status(401).json({ message: 'Authentication required' });
      return;
    }

    // Extract token
    const token = authHeader.split(' ')[1];

    // Verify token
    const decoded = verifyToken(token);

    if (!decoded) {
      res.status(401).json({ message: 'Invalid or expired token' });
      return;
    }

    // Set user in request
    req.user = decoded;

    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(401).json({ message: 'Authentication failed' });
  }
};

// Export authentication middleware directly
export const authenticate: RequestHandler = authenticateImpl;

// Role-based authorization middleware
export const authorize = (...roles: UserRole[]): RequestHandler => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      res.status(401).json({ message: 'Authentication required' });
      return;
    }

    if (!roles.includes(req.user.role)) {
      res.status(403).json({ message: 'Access denied' });
      return;
    }

    next();
  };
};

// Tenant access middleware implementation
const checkTenantAccessImpl = (req: Request, res: Response, next: NextFunction): void => {
  if (!req.user) {
    res.status(401).json({ message: 'Authentication required' });
    return;
  }

  // Super admin has access to all tenants
  if (req.user.role === UserRole.SUPER_ADMIN) {
    next();
    return;
  }

  // Get tenant ID from request parameters or body
  const tenantId = req.params.tenantId || req.body.tenantId;

  if (!tenantId) {
    res.status(400).json({ message: 'Tenant ID is required' });
    return;
  }

  // Check if user has access to the tenant
  if (req.user.tenantId !== tenantId && req.user.role !== UserRole.ADMIN) {
    res.status(403).json({ message: 'Access to this tenant is denied' });
    return;
  }

  next();
};

// Export tenant access middleware directly
export const checkTenantAccess: RequestHandler = checkTenantAccessImpl;

