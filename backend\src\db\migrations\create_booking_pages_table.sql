-- Create booking pages table
CREATE TABLE IF NOT EXISTS booking_pages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  title TEXT NOT NULL DEFAULT 'Book an Appointment',
  description TEXT,
  primary_color TEXT DEFAULT '#3b82f6',
  show_logo BOOLEAN DEFAULT TRUE,
  custom_css TEXT,
  custom_js TEXT,
  custom_confirmation_message TEXT,
  require_phone BOOLEAN DEFAULT TRUE,
  require_address BOOLEAN DEFAULT FALSE,
  show_prices BOOLEAN DEFAULT TRUE,
  show_duration BOOLEAN DEFAULT TRUE,
  allow_cancellation BOOLEAN DEFAULT TRUE,
  cancellation_period_hours INTEGER DEFAULT 24,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create index on tenant_id
CREATE INDEX idx_booking_pages_tenant_id ON booking_pages(tenant_id);

-- Create RLS policies
ALTER TABLE booking_pages ENABLE ROW LEVEL SECURITY;

-- Policy for public to view booking pages
CREATE POLICY booking_pages_select_policy ON booking_pages
  FOR SELECT
  USING (TRUE);

-- Policy for tenant owners to insert booking pages
CREATE POLICY booking_pages_insert_policy ON booking_pages
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM tenants t
      WHERE t.id = tenant_id
      AND t.owner_id = auth.uid()
    )
  );

-- Policy for tenant owners to update booking pages
CREATE POLICY booking_pages_update_policy ON booking_pages
  FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM tenants t
      WHERE t.id = tenant_id
      AND t.owner_id = auth.uid()
    )
  );
