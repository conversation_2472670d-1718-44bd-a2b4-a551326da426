import api from './api';
import eventBus, { EVENT_TYPES } from '../utils/eventBus';

export interface AppointmentData {
  clientId: string;
  staffId: string;
  serviceId: string;
  startTime: string;
  notes?: string;
}

export enum AppointmentStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  CANCELLED = 'cancelled',
  COMPLETED = 'completed',
  NO_SHOW = 'no_show',
}

export interface TimeSlot {
  startTime: string;
  endTime: string;
}

const appointmentService = {
  // Get appointments for a tenant
  getAppointments: async (tenantId: string, params?: {
    startDate?: string;
    endDate?: string;
    staffId?: string;
    clientId?: string;
    status?: AppointmentStatus;
  }) => {
    const response = await api.get(`/appointments/${tenantId}/appointments`, { params });
    return response.data;
  },

  // Get appointment by ID
  getAppointmentById: async (tenantId: string, appointmentId: string) => {
    const response = await api.get(`/appointments/${tenantId}/appointments/${appointmentId}`);
    return response.data;
  },

  // Create a new appointment
  createAppointment: async (tenantId: string, data: AppointmentData) => {
    const response = await api.post(`/appointments/${tenantId}/appointments`, data);
    // Emit event to notify components that an appointment was created
    eventBus.emit(EVENT_TYPES.APPOINTMENT_CREATED, response.data);
    eventBus.emit(EVENT_TYPES.DATA_CHANGED);
    return response.data;
  },

  // Update an appointment
  updateAppointment: async (tenantId: string, appointmentId: string, data: Partial<AppointmentData & { status?: AppointmentStatus }>) => {
    const response = await api.put(`/appointments/${tenantId}/appointments/${appointmentId}`, data);
    // Emit event to notify components that an appointment was updated
    eventBus.emit(EVENT_TYPES.APPOINTMENT_UPDATED, response.data);
    eventBus.emit(EVENT_TYPES.DATA_CHANGED);
    return response.data;
  },

  // Cancel an appointment
  cancelAppointment: async (tenantId: string, appointmentId: string) => {
    const response = await api.put(`/appointments/${tenantId}/appointments/${appointmentId}`, {
      status: AppointmentStatus.CANCELLED,
    });
    // Emit event to notify components that an appointment was cancelled (updated)
    eventBus.emit(EVENT_TYPES.APPOINTMENT_UPDATED, response.data);
    eventBus.emit(EVENT_TYPES.DATA_CHANGED);
    return response.data;
  },

  // Get available time slots
  getAvailableTimeSlots: async (tenantId: string, params: {
    serviceId: string;
    staffId: string;
    date: string;
    excludeAppointmentId?: string;
  }) => {
    const response = await api.get(`/appointments/${tenantId}/timeslots`, { params });
    return response.data as { timeSlots: TimeSlot[] };
  },

  // Delete an appointment
  deleteAppointment: async (tenantId: string, appointmentId: string) => {
    const response = await api.delete(`/appointments/${tenantId}/appointments/${appointmentId}`);
    // Emit event to notify components that an appointment was deleted
    eventBus.emit(EVENT_TYPES.APPOINTMENT_DELETED, { id: appointmentId, tenantId });
    eventBus.emit(EVENT_TYPES.DATA_CHANGED);
    return response.data;
  },
};

export default appointmentService;
