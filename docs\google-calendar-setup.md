# Google Calendar Integration Setup Guide

This guide will help you set up Google Calendar integration for your Scheduly application.

## Step 1: Create a Google Cloud Project

1. Go to the [Google Cloud Console](https://console.cloud.google.com/)
2. Click on the project dropdown at the top of the page
3. Click on "New Project"
4. Enter a name for your project (e.g., "Scheduly")
5. Click "Create"
6. Wait for the project to be created and then select it from the dropdown

## Step 2: Enable the Google Calendar API

1. In the Google Cloud Console, go to "APIs & Services" > "Library"
2. Search for "Google Calendar API"
3. Click on "Google Calendar API" in the results
4. Click "Enable"

## Step 3: Configure OAuth Consent Screen

1. Go to "APIs & Services" > "OAuth consent screen"
2. Select "External" as the user type (unless you have a Google Workspace account)
3. Click "Create"
4. Fill in the required information:
   - App name: "Scheduly"
   - User support email: Your email address
   - Developer contact information: Your email address
5. Click "Save and Continue"
6. Add the following scopes:
   - `https://www.googleapis.com/auth/calendar`
   - `https://www.googleapis.com/auth/calendar.events`
7. Click "Save and Continue"
8. Add test users (your email address)
9. Click "Save and Continue"
10. Review your settings and click "Back to Dashboard"

## Step 4: Create OAuth Client ID

1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth client ID"
3. Select "Web application" as the application type
4. Name: "Scheduly Web Client"
5. Add authorized JavaScript origins:
   - `http://localhost:5173`
6. Add authorized redirect URIs:
   - `http://localhost:5173/google-calendar-callback`
7. Click "Create"
8. A popup will appear with your client ID and client secret. Copy these values.

## Step 5: Update Environment Variables

1. Open the `.env` file in the backend directory
2. Update the Google OAuth configuration with your actual credentials:

```
# Google OAuth Configuration
GOOGLE_CLIENT_ID=your_actual_client_id.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=your_actual_client_secret
GOOGLE_REDIRECT_URI=http://localhost:5173/google-calendar-callback
```

3. Save the file

## Step 6: Restart the Backend Server

1. Stop the backend server if it's running
2. Start the backend server again:
   ```
   cd backend
   npm run dev
   ```

## Troubleshooting

If you encounter the "OAuth client was not found" error:

1. Double-check that your client ID and client secret are correctly copied into the `.env` file
2. Verify that the redirect URI in your Google Cloud Console matches exactly with the one in your `.env` file
3. Make sure you've enabled the Google Calendar API
4. Check that your OAuth consent screen is properly configured
5. If you're in testing mode, ensure your email is added as a test user

## Additional Notes

- The Google OAuth consent screen will be in "Testing" mode by default, which means only authorized test users can use it
- If you want to make your application available to all users, you'll need to publish your app by going through the verification process
- OAuth credentials are sensitive information. Never commit them to version control.
