import api from './api';
import eventBus, { EVENT_TYPES } from '../utils/eventBus';

export enum UserRole {
  ADMIN = 'admin',
  STAFF = 'staff',
  CLIENT = 'client',
  SUPER_ADMIN = 'super_admin',
}

export interface UserData {
  firstName: string;
  lastName: string;
  email: string;
  password?: string;
  phone?: string;
  role?: UserRole;
}

export interface ProfileUpdateData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
}

const userService = {
  // Get all users for a tenant
  getUsers: async (tenantId: string, role?: UserRole) => {
    const response = await api.get(`/users/${tenantId}/users`, { params: { role } });
    return response.data;
  },

  // Get user by ID
  getUserById: async (tenantId: string, userId: string) => {
    const response = await api.get(`/users/${tenantId}/users/${userId}`);
    return response.data;
  },

  // Create a new user
  createUser: async (tenantId: string, data: UserData) => {
    const response = await api.post(`/users/${tenantId}/users`, data);

    // Emit events based on user role
    if (data.role === UserRole.CLIENT) {
      eventBus.emit(EVENT_TYPES.CLIENT_CREATED, response.data);
    }
    eventBus.emit(EVENT_TYPES.DATA_CHANGED);

    return response.data;
  },

  // Update a user
  updateUser: async (tenantId: string, userId: string, data: Partial<UserData & { active?: boolean }>) => {
    const response = await api.put(`/users/${tenantId}/users/${userId}`, data);

    // Emit event for data change
    eventBus.emit(EVENT_TYPES.DATA_CHANGED);

    return response.data;
  },

  // Delete a user
  deleteUser: async (tenantId: string, userId: string) => {
    const response = await api.delete(`/users/${tenantId}/users/${userId}`);

    // Emit events for user deletion
    eventBus.emit(EVENT_TYPES.CLIENT_DELETED, { id: userId, tenantId });
    eventBus.emit(EVENT_TYPES.DATA_CHANGED);

    return response.data;
  },

  // Update current user's profile
  updateProfile: async (data: ProfileUpdateData) => {
    const response = await api.put('/users/profile', data);
    return response.data;
  },
};

export default userService;
