import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { tenantService } from '../services';
import { 
  formatDateWithTimezone, 
  formatTimeWithTimezone, 
  getCurrentDateInTimezone 
} from '../utils/timezoneUtils';

interface TimezoneContextType {
  timezone: string;
  setTimezone: (timezone: string) => void;
  formatDate: (date: Date | string) => string;
  formatTime: (date: Date | string) => string;
  getCurrentDate: () => Date;
}

const TimezoneContext = createContext<TimezoneContextType | undefined>(undefined);

export const TimezoneProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { tenant } = useAuth();
  const [timezone, setTimezone] = useState<string>('Asia/Kolkata');

  useEffect(() => {
    const fetchTenantTimezone = async () => {
      if (!tenant) return;

      try {
        const tenantData = await tenantService.getTenant(tenant.id);
        if (tenantData.timezone) {
          setTimezone(tenantData.timezone);
        }
      } catch (error) {
        console.error('Error fetching tenant timezone:', error);
      }
    };

    fetchTenantTimezone();
  }, [tenant]);

  const formatDate = (date: Date | string) => {
    return formatDateWithTimezone(date, timezone);
  };

  const formatTime = (date: Date | string) => {
    return formatTimeWithTimezone(date, timezone);
  };

  const getCurrentDate = () => {
    return getCurrentDateInTimezone(timezone);
  };

  return (
    <TimezoneContext.Provider
      value={{
        timezone,
        setTimezone,
        formatDate,
        formatTime,
        getCurrentDate,
      }}
    >
      {children}
    </TimezoneContext.Provider>
  );
};

export const useTimezone = (): TimezoneContextType => {
  const context = useContext(TimezoneContext);
  if (context === undefined) {
    throw new Error('useTimezone must be used within a TimezoneProvider');
  }
  return context;
};
