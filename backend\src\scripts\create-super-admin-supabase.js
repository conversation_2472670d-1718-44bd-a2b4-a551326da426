// This script creates a super admin user using Supabase
// Run with: node create-super-admin-supabase.js

const { createClient } = require('@supabase/supabase-js');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

// Supabase configuration
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase URL or Service Role Key. Please check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Super admin details - customize these as needed
const superAdmin = {
  id: uuidv4(),
  firstName: 'Super',
  lastName: 'Admin',
  email: '<EMAIL>',
  password: 'Admin123!', // Change this to a secure password
  role: 'super_admin',
};

async function createSuperAdmin() {
  try {
    console.log('Creating a super admin user...');
    
    // Check if super admin already exists
    const { data: existingUser, error: checkError } = await supabase
      .from('users')
      .select('*')
      .eq('email', superAdmin.email)
      .eq('role', superAdmin.role)
      .single();

    if (checkError && checkError.code !== 'PGRST116') { // PGRST116 means no rows found
      console.error('Error checking for existing user:', checkError);
      return;
    }

    if (existingUser) {
      console.log('Super admin already exists. Updating password...');
      
      // Hash password
      const hashedPassword = await bcrypt.hash(superAdmin.password, 10);
      
      // Update user
      const { data: updatedUser, error: updateError } = await supabase
        .from('users')
        .update({ 
          password: hashedPassword, 
          active: true 
        })
        .eq('email', superAdmin.email)
        .eq('role', superAdmin.role)
        .select()
        .single();

      if (updateError) {
        console.error('Error updating user:', updateError);
        return;
      }

      console.log('Super admin updated:', {
        id: updatedUser.id,
        email: updatedUser.email,
        role: updatedUser.role,
      });
    } else {
      // Hash password
      const hashedPassword = await bcrypt.hash(superAdmin.password, 10);
      
      // Create user - note: super admin doesn't need a tenant_id
      const { data: newUser, error: insertError } = await supabase
        .from('users')
        .insert({
          id: superAdmin.id,
          tenant_id: null,
          first_name: superAdmin.firstName,
          last_name: superAdmin.lastName,
          email: superAdmin.email,
          password: hashedPassword,
          role: superAdmin.role,
          active: true
        })
        .select()
        .single();

      if (insertError) {
        console.error('Error creating user:', insertError);
        return;
      }

      console.log('Super admin created:', {
        id: newUser.id,
        email: newUser.email,
        role: newUser.role,
      });
    }
    
    console.log('\nSuper admin login credentials:');
    console.log('Email:', superAdmin.email);
    console.log('Password:', superAdmin.password);
    console.log('\nPlease change the password after first login for security reasons.');
    
  } catch (error) {
    console.error('Error creating super admin:', error);
  }
}

// Run the function
createSuperAdmin();
