import { Activity, NotificationType } from '../services/types';
import { AppointmentStatus } from '../services/appointmentService';

// Function to generate activities from appointments
export const generateActivitiesFromAppointments = (appointments: any[]): Activity[] => {
  if (!appointments || appointments.length === 0) {
    return [];
  }

  return appointments.map(appointment => {
    let type: string;
    let title: string;
    let description: string;
    let icon: string;
    let iconColor: string;

    const clientName = `${appointment.client.firstName} ${appointment.client.lastName}`;
    const serviceName = appointment.service.name;
    const appointmentTime = new Date(appointment.startTime).toLocaleTimeString([], {
      hour: '2-digit',
      minute: '2-digit'
    });
    const appointmentDate = new Date(appointment.startTime).toLocaleDateString([], {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });

    switch (appointment.status) {
      case AppointmentStatus.CONFIRMED:
        type = NotificationType.APPOINTMENT_CREATED;
        title = 'New appointment created';
        description = `${clientName} booked a ${serviceName} for ${appointmentDate} at ${appointmentTime}`;
        icon = 'calendar';
        iconColor = 'blue';
        break;
      case AppointmentStatus.COMPLETED:
        type = NotificationType.APPOINTMENT_UPDATED;
        title = 'Appointment completed';
        description = `${clientName}'s ${serviceName} appointment was marked as completed`;
        icon = 'check-circle';
        iconColor = 'green';
        break;
      case AppointmentStatus.CANCELLED:
        type = NotificationType.APPOINTMENT_CANCELLED;
        title = 'Appointment cancelled';
        description = `${clientName}'s ${serviceName} appointment was cancelled`;
        icon = 'x-circle';
        iconColor = 'red';
        break;
      default:
        type = NotificationType.APPOINTMENT_UPDATED;
        title = 'Appointment updated';
        description = `${clientName}'s ${serviceName} appointment was updated`;
        icon = 'refresh-cw';
        iconColor = 'blue';
    }

    return {
      id: appointment.id,
      type,
      title,
      description,
      timestamp: appointment.updatedAt || appointment.createdAt,
      relatedId: appointment.id,
      relatedType: 'appointment',
      icon,
      iconColor
    };
  });
};

// Function to generate activities from clients
export const generateActivitiesFromClients = (clients: any[]): Activity[] => {
  if (!clients || clients.length === 0) {
    return [];
  }

  return clients.map(client => {
    return {
      id: `client-${client.id}`,
      type: NotificationType.CLIENT_ADDED,
      title: 'New client added',
      description: `${client.firstName} ${client.lastName} was added to your client list`,
      timestamp: client.createdAt,
      relatedId: client.id,
      relatedType: 'client',
      icon: 'users',
      iconColor: 'purple'
    };
  });
};

// Function to generate activities from services
export const generateActivitiesFromServices = (services: any[]): Activity[] => {
  if (!services || services.length === 0) {
    return [];
  }

  return services.map(service => {
    return {
      id: `service-${service.id}`,
      type: 'service_added',
      title: 'New service added',
      description: `${service.name} service was added (${service.duration} min, ₹${service.price})`,
      timestamp: service.createdAt,
      relatedId: service.id,
      relatedType: 'service',
      icon: 'dollar-sign',
      iconColor: 'green'
    };
  });
};

// Function to combine and sort activities
export const combineAndSortActivities = (activities: Activity[]): Activity[] => {
  return activities.sort((a, b) => {
    return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
  });
};

// Main function to get recent activities
export const getRecentActivities = async (
  appointments: any[],
  clients: any[] = [],
  services: any[] = [],
  limit: number = 5
): Promise<Activity[]> => {
  // Generate activities from different sources
  const appointmentActivities = generateActivitiesFromAppointments(appointments);
  const clientActivities = generateActivitiesFromClients(clients);
  const serviceActivities = generateActivitiesFromServices(services);

  // Combine all activities
  const allActivities = [
    ...appointmentActivities,
    ...clientActivities,
    ...serviceActivities
  ];

  // Sort by timestamp (newest first)
  const sortedActivities = combineAndSortActivities(allActivities);

  // Return limited number of activities
  return sortedActivities.slice(0, limit);
};
