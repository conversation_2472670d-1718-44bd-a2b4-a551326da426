import { Request, Response, NextFunction } from 'express';
import { canCreateAppointment } from '../services/appointmentService';

/**
 * Middleware to check if a user can create more appointments
 */
export async function checkAppointmentLimit(
  req: Request,
  res: Response,
  next: NextFunction
) {
  try {
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }
    
    // Check if user can create more appointments (delegated to appointmentService)
    const { canCreate, reason, limit, current } = await canCreateAppointment(userId);
    
    if (!canCreate) {
      return res.status(403).json({
        success: false,
        message: reason || 'You have reached your appointment limit',
        limit,
        current,
        upgradeRequired: true
      });
    }
    
    // User can create more appointments, proceed
    next();
  } catch (error) {
    console.error('Error in checkAppointmentLimit middleware:', error);
    return res.status(500).json({
      success: false,
      message: 'An error occurred while checking appointment limits'
    });
  }
}
