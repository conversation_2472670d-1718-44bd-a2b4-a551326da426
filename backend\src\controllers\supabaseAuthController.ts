import { Request, Response } from 'express';
import { supabase } from '../config/supabase';
import { generateToken } from '../utils/jwt';
import bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import crypto from 'crypto';
import { UserRole } from '../models/supabase';

// Login user
export const login = async (req: Request, res: Response) => {
  try {
    console.log('Login attempt:', req.body.email);
    const { email, password, tenantId } = req.body;

    // Find user by email and tenant ID
    let query = supabase
      .from('users')
      .select(`
        *,
        tenant:tenants(*)
      `)
      .eq('email', email)
      .eq('active', true);

    // Add tenant filter if provided
    if (tenantId) {
      query = query.eq('tenant_id', tenantId);
    }

    const { data: users, error } = await query;

    if (error) {
      console.error('Error fetching user:', error);
      return res.status(500).json({ message: 'Server error' });
    }

    if (!users || users.length === 0) {
      console.log('User not found:', email);
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    const user = users[0];

    // Check if user is active
    if (!user.active) {
      console.log('User is inactive:', email);
      return res.status(401).json({ message: 'Account is inactive' });
    }

    // Check if tenant is active
    if (user.tenant && !user.tenant.active) {
      console.log('Tenant is inactive for user:', email);
      return res.status(401).json({ message: 'Business account is inactive' });
    }

    // Check password
    if (!user.password) {
      console.log('User has no password:', email);
      return res.status(401).json({ message: 'Invalid login method' });
    }

    console.log('Checking password for user:', email);

    try {
      // Use bcrypt directly for more reliable password comparison
      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (!isPasswordValid) {
        console.log('Invalid password for user:', email);
        return res.status(401).json({ message: 'Invalid credentials' });
      }
    } catch (error) {
      console.error('Password comparison error:', error);
      return res.status(500).json({ message: 'Login failed' });
    }

    // Generate JWT token
    const token = generateToken({
      id: user.id,
      email: user.email,
      role: user.role,
      tenantId: user.tenant_id
    });

    console.log('Login successful for user:', email);
    res.json({
      message: 'Login successful',
      token,
      user: {
        id: user.id,
        firstName: user.first_name,
        lastName: user.last_name,
        email: user.email,
        role: user.role,
        tenantId: user.tenant_id,
      },
      tenant: user.tenant ? {
        id: user.tenant.id,
        name: user.tenant.name,
      } : null,
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({ message: 'Login failed' });
  }
};

// Register a new tenant (business)
export const registerTenant = async (req: Request, res: Response) => {
  try {
    const {
      name,
      email,
      password,
      phone,
      address,
      firstName,
      lastName
    } = req.body;

    // Check if tenant with email already exists
    const { data: existingTenant, error: tenantError } = await supabase
      .from('tenants')
      .select('*')
      .eq('email', email)
      .single();

    if (tenantError && tenantError.code !== 'PGRST116') {
      console.error('Error checking tenant:', tenantError);
      return res.status(500).json({ message: 'Server error' });
    }

    if (existingTenant) {
      return res.status(400).json({ message: 'Business with this email already exists' });
    }

    // Create new tenant
    const tenantId = uuidv4();
    const { data: tenant, error: createTenantError } = await supabase
      .from('tenants')
      .insert({
        id: tenantId,
        name,
        email,
        phone,
        address,
        active: true
      })
      .select()
      .single();

    if (createTenantError) {
      console.error('Error creating tenant:', createTenantError);
      return res.status(500).json({ message: 'Failed to create business' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create admin user for the tenant
    const userId = uuidv4();
    const { data: adminUser, error: createUserError } = await supabase
      .from('users')
      .insert({
        id: userId,
        tenant_id: tenantId,
        first_name: firstName,
        last_name: lastName,
        email,
        password: hashedPassword,
        phone,
        role: UserRole.ADMIN,
        active: true
      })
      .select()
      .single();

    if (createUserError) {
      console.error('Error creating user:', createUserError);
      // Try to rollback tenant creation
      await supabase.from('tenants').delete().eq('id', tenantId);
      return res.status(500).json({ message: 'Failed to create user' });
    }

    // Generate JWT token
    const token = generateToken({
      id: userId,
      email,
      role: UserRole.ADMIN,
      tenantId
    });

    res.status(201).json({
      message: 'Tenant registered successfully',
      token,
      user: {
        id: adminUser.id,
        firstName: adminUser.first_name,
        lastName: adminUser.last_name,
        email: adminUser.email,
        role: adminUser.role,
      },
      tenant: {
        id: tenant.id,
        name: tenant.name,
      },
    });
  } catch (error) {
    console.error('Tenant registration error:', error);
    res.status(500).json({ message: 'Failed to register tenant' });
  }
};

// Get current user profile
export const getProfile = async (req: Request, res: Response) => {
  try {
    console.log('Fetching user profile...');

    if (!req.user) {
      console.error('No user in request object');
      return res.status(401).json({
        success: false,
        message: 'Authentication required'
      });
    }

    console.log(`Fetching profile for user ID: ${req.user.id}`);

    try {
      const { data: user, error } = await supabase
        .from('users')
        .select(`
          *,
          tenant:tenants(id, name, logo, primary_color, secondary_color)
        `)
        .eq('id', req.user.id)
        .single();

      if (error) {
        console.error('Error fetching user from Supabase:', error);
        return res.status(500).json({
          success: false,
          message: 'Database error while fetching profile',
          error: error.message
        });
      }

      if (!user) {
        console.error(`User not found with ID: ${req.user.id}`);
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      console.log(`Successfully fetched profile for user: ${user.email}`);

      return res.json({
        success: true,
        data: {
          user: {
            id: user.id,
            firstName: user.first_name,
            lastName: user.last_name,
            email: user.email,
            phone: user.phone,
            role: user.role,
            tenantId: user.tenant_id,
            createdAt: user.created_at,
          },
          tenant: user.tenant ? {
            id: user.tenant.id,
            name: user.tenant.name,
            logo: user.tenant.logo,
            primaryColor: user.tenant.primary_color,
            secondaryColor: user.tenant.secondary_color,
          } : null,
        }
      });
    } catch (dbError) {
      console.error('Database operation error:', dbError);
      return res.status(500).json({
        success: false,
        message: 'Database operation failed',
        error: dbError instanceof Error ? dbError.message : String(dbError)
      });
    }
  } catch (error) {
    console.error('Unexpected error in getProfile:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to get profile',
      error: error instanceof Error ? error.message : String(error)
    });
  }
};

// Change user password
export const changePassword = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { currentPassword, newPassword } = req.body;

    // Validate input
    if (!currentPassword || !newPassword) {
      return res.status(400).json({ message: 'Current password and new password are required' });
    }

    if (newPassword.length < 8) {
      return res.status(400).json({ message: 'New password must be at least 8 characters long' });
    }

    // Get user with password
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('id', req.user.id)
      .single();

    if (error) {
      console.error('Error fetching user:', error);
      return res.status(500).json({ message: 'Server error' });
    }

    if (!user) {
      return res.status(404).json({ message: 'User not found' });
    }

    // Verify current password
    const isPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isPasswordValid) {
      return res.status(401).json({ message: 'Current password is incorrect' });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password
    const { error: updateError } = await supabase
      .from('users')
      .update({ password: hashedPassword })
      .eq('id', req.user.id);

    if (updateError) {
      console.error('Error updating password:', updateError);
      return res.status(500).json({ message: 'Failed to update password' });
    }

    return res.json({
      success: true,
      message: 'Password updated successfully'
    });
  } catch (error) {
    console.error('Change password error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to change password'
    });
  }
};

// Request password reset
export const requestPasswordReset = async (req: Request, res: Response) => {
  try {
    const { email } = req.body;

    if (!email) {
      return res.status(400).json({ message: 'Email is required' });
    }

    // Find user by email
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('email', email)
      .eq('active', true)
      .single();

    if (error || !user) {
      // Don't reveal if user exists for security reasons
      return res.json({ message: 'If your email is registered, you will receive a password reset link' });
    }

    // Generate reset token (expires in 1 hour)
    const resetToken = crypto.randomBytes(32).toString('hex');
    const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 hour from now

    // Store reset token in database
    const { error: updateError } = await supabase
      .from('users')
      .update({
        reset_token: resetToken,
        reset_token_expiry: resetTokenExpiry.toISOString()
      })
      .eq('id', user.id);

    if (updateError) {
      console.error('Error storing reset token:', updateError);
      return res.status(500).json({ message: 'Failed to process password reset request' });
    }

    // Import the sendPasswordResetEmail function
    const { sendPasswordResetEmail } = await import('../utils/resendEmail');

    // Construct the reset URL (frontend URL)
    const resetUrl = process.env.FRONTEND_URL
      ? `${process.env.FRONTEND_URL}/reset-password`
      : 'http://localhost:5173/reset-password';

    // Send password reset email
    const emailSent = await sendPasswordResetEmail(email, resetToken, resetUrl);

    if (!emailSent) {
      console.warn(`Failed to send password reset email to ${email}, but token was created.`);
      // We still return success to the user for security reasons
    } else {
      console.log(`Password reset email sent to ${email}`);
    }

    return res.json({
      success: true,
      message: 'If your email is registered, you will receive a password reset link'
    });
  } catch (error) {
    console.error('Password reset request error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to process password reset request'
    });
  }
};

// Reset password with token
export const resetPassword = async (req: Request, res: Response) => {
  try {
    const { token, newPassword } = req.body;

    if (!token || !newPassword) {
      return res.status(400).json({ message: 'Token and new password are required' });
    }

    if (newPassword.length < 8) {
      return res.status(400).json({ message: 'New password must be at least 8 characters long' });
    }

    // Find user with this reset token
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('reset_token', token)
      .eq('active', true)
      .single();

    if (error || !user) {
      return res.status(400).json({ message: 'Invalid or expired reset token' });
    }

    // Check if token is expired
    const tokenExpiry = new Date(user.reset_token_expiry);
    if (tokenExpiry < new Date()) {
      return res.status(400).json({ message: 'Reset token has expired' });
    }

    // Hash new password
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    // Update password and clear reset token
    const { error: updateError } = await supabase
      .from('users')
      .update({
        password: hashedPassword,
        reset_token: null,
        reset_token_expiry: null
      })
      .eq('id', user.id);

    if (updateError) {
      console.error('Error resetting password:', updateError);
      return res.status(500).json({ message: 'Failed to reset password' });
    }

    return res.json({
      success: true,
      message: 'Password has been reset successfully'
    });
  } catch (error) {
    console.error('Password reset error:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to reset password'
    });
  }
};
