import { Request, Response, NextFunction } from 'express';
import { SubscriptionFeature } from '../models/SubscriptionFeature';
import { TierLevel, tierHasFeature } from '../models/SubscriptionTier';
import { getUserSubscription } from '../services/subscriptionService';
import { isSubscriptionActive } from '../models/Subscription';

// Middleware to check if user has access to a specific feature
export async function requireFeature(feature: SubscriptionFeature) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }
      
      // Get user's active subscription
      const subscription = await getUserSubscription(userId);
      
      // Check if subscription is active
      if (!isSubscriptionActive(subscription)) {
        return res.status(403).json({ 
          success: false, 
          message: 'No active subscription found',
          upgradeRequired: true
        });
      }
      
      // Check if the user's tier has access to the requested feature
      const userTier = subscription.tier_id;
      
      if (!tierHasFeature(userTier, feature)) {
        return res.status(403).json({ 
          success: false, 
          message: `Your current plan doesn't include access to this feature`,
          feature: feature,
          upgradeRequired: true,
          currentTier: userTier
        });
      }
      
      // User has access to the feature
      next();
    } catch (error) {
      console.error('Error in subscription middleware:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Internal server error checking subscription' 
      });
    }
  };
}

// Middleware to check if user has a specific tier or higher
export async function requireTier(minimumTier: TierLevel) {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }
      
      // Get user's active subscription
      const subscription = await getUserSubscription(userId);
      
      // Check if subscription is active
      if (!isSubscriptionActive(subscription)) {
        return res.status(403).json({ 
          success: false, 
          message: 'No active subscription found',
          upgradeRequired: true
        });
      }
      
      // Check if the user's tier meets the minimum requirement
      const userTier = subscription.tier_id;
      const tierLevels = [TierLevel.FREE, TierLevel.PRO, TierLevel.PRO_PLUS];
      const userTierIndex = tierLevels.indexOf(userTier);
      const requiredTierIndex = tierLevels.indexOf(minimumTier);
      
      if (userTierIndex < requiredTierIndex) {
        return res.status(403).json({ 
          success: false, 
          message: `This feature requires ${minimumTier} tier or higher`,
          upgradeRequired: true,
          currentTier: userTier,
          requiredTier: minimumTier
        });
      }
      
      // User has the required tier
      next();
    } catch (error) {
      console.error('Error in tier middleware:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Internal server error checking subscription tier' 
      });
    }
  };
}

// Middleware to check appointment limits
export async function checkAppointmentLimits() {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }
      
      // Get user's active subscription
      const subscription = await getUserSubscription(userId);
      
      // Check if subscription is active
      if (!isSubscriptionActive(subscription)) {
        return res.status(403).json({ 
          success: false, 
          message: 'No active subscription found',
          upgradeRequired: true
        });
      }
      
      // Get the tier details
      const userTier = subscription.tier_id;
      const { maxAppointmentsPerMonth } = await import('../services/subscriptionService')
        .then(module => module.getTierDetails(userTier));
      
      // If unlimited appointments, proceed
      if (maxAppointmentsPerMonth === null) {
        return next();
      }
      
      // Check current month's appointment count
      const { getMonthlyAppointmentCount } = await import('../services/appointmentService');
      const count = await getMonthlyAppointmentCount(userId);
      
      if (count >= maxAppointmentsPerMonth) {
        return res.status(403).json({ 
          success: false, 
          message: `You've reached your monthly appointment limit (${maxAppointmentsPerMonth})`,
          upgradeRequired: true,
          currentTier: userTier,
          limit: maxAppointmentsPerMonth,
          current: count
        });
      }
      
      // User is within limits
      next();
    } catch (error) {
      console.error('Error in appointment limits middleware:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Internal server error checking appointment limits' 
      });
    }
  };
}

// Middleware to check staff member limits
export async function checkStaffLimits() {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const userId = req.user?.id;
      
      if (!userId) {
        return res.status(401).json({ 
          success: false, 
          message: 'Authentication required' 
        });
      }
      
      // Get user's active subscription
      const subscription = await getUserSubscription(userId);
      
      // Check if subscription is active
      if (!isSubscriptionActive(subscription)) {
        return res.status(403).json({ 
          success: false, 
          message: 'No active subscription found',
          upgradeRequired: true
        });
      }
      
      // Get the tier details
      const userTier = subscription.tier_id;
      const { maxStaffMembers } = await import('../services/subscriptionService')
        .then(module => module.getTierDetails(userTier));
      
      // If unlimited staff members, proceed
      if (maxStaffMembers === null) {
        return next();
      }
      
      // Check current staff count (excluding the owner)
      const { getStaffCount } = await import('../services/staffService');
      const count = await getStaffCount(userId);
      
      // If adding a new staff member would exceed the limit
      if (req.method === 'POST' && count >= maxStaffMembers) {
        return res.status(403).json({ 
          success: false, 
          message: `You've reached your staff member limit (${maxStaffMembers})`,
          upgradeRequired: true,
          currentTier: userTier,
          limit: maxStaffMembers,
          current: count
        });
      }
      
      // User is within limits
      next();
    } catch (error) {
      console.error('Error in staff limits middleware:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Internal server error checking staff limits' 
      });
    }
  };
}
