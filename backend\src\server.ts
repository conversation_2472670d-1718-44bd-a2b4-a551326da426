import express, { Express, Request, Response } from 'express';
import cors from 'cors';
import * as dotenv from 'dotenv';
import path from 'path';
// Import Supabase
import { supabase, testConnection as testSupabaseConnection } from './config/supabase';
// Import essential routes only
import authRoutes from './routes/authRoutes';
import superAdminRoutes from './routes/superAdminRoutes';
import adminSubscriptionRoutes from './routes/adminSubscriptionRoutes';
import { notFound, errorHandler } from './middleware/error';
import { initializeScheduler } from './utils/scheduler';

// Load environment variables
dotenv.config();

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit the process, just log the error
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // Don't exit the process, just log the error
});

// Initialize Express app
const app: Express = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Add simple request logging
app.use((req, res, next) => {
  console.log(`${req.method} ${req.url}`);
  next();
});

// Routes
app.get('/', (req: Request, res: Response) => {
  res.send('Scheduly API is running');
});

// Serve documentation
app.get('/docs/:filename', (req: Request, res: Response) => {
  const { filename } = req.params;
  const filePath = path.join(__dirname, '..', 'docs', filename);

  res.sendFile(filePath, (err) => {
    if (err) {
      console.error(`Error serving documentation file ${filename}:`, err);
      res.status(404).send('Documentation file not found');
    }
  });
});

// API routes (only essential ones for testing)
console.log('Registering API routes...');

// Debug: Check if routes are properly imported
console.log('Auth routes type:', typeof authRoutes);
console.log('Super admin routes type:', typeof superAdminRoutes);
console.log('Admin subscription routes type:', typeof adminSubscriptionRoutes);

app.use('/api/auth', authRoutes);
console.log('✅ Auth routes registered');

// Create a simple test router
const testRouter = express.Router();
testRouter.get('/test', (req, res) => {
  res.json({ message: 'Test router working!' });
});
app.use('/api/super-admin', testRouter);
console.log('✅ Test super admin routes registered');

app.use('/api/admin/subscription', adminSubscriptionRoutes);
console.log('✅ Admin subscription routes registered');

// API root health/status endpoint
app.get('/api', (req: Request, res: Response) => {
  res.send('Scheduly API root. See /api/auth, /api/super-admin, etc.');
});

// Test endpoint to verify routing works
app.get('/api/test', (req: Request, res: Response) => {
  res.json({ message: 'Test endpoint working', timestamp: new Date().toISOString() });
});

// Error handling middleware (must be last)
app.use(notFound);
app.use(errorHandler);

// Start server
const startServer = async () => {
  try {
    console.log('Starting server without connection test...');

    // Start server
    app.listen(port, () => {
      console.log(`Server is running on port ${port}`);
      console.log(`API available at http://localhost:${port}/api`);

      // Initialize the scheduler
      try {
        initializeScheduler();
        console.log('Scheduler initialized');
      } catch (error) {
        console.warn('Failed to initialize scheduler:', error);
      }
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();

