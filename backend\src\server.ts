import express, { Express, Request, Response } from 'express';
import cors from 'cors';
import * as dotenv from 'dotenv';
import path from 'path';
// Import Supabase
import { supabase, testConnection as testSupabaseConnection } from './config/supabase';
import {
  authRoutes,
  tenantRoutes,
  userRoutes,
  serviceRoutes,
  availabilityRoutes,
  appointmentRoutes,
  notificationRoutes,
  googleCalendarRoutes,
  superAdminRoutes,
  subscriptionRoutes,
  razorpayRoutes,
  usageRoutes,
  adminSubscriptionRoutes
} from './routes';
import { notFound, errorHandler } from './middleware/error';
import { initializeScheduler } from './utils/scheduler';

// Load environment variables
dotenv.config();

// Initialize Express app
const app: Express = express();
const port = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Routes
app.get('/', (req: Request, res: Response) => {
  res.send('Scheduly API is running');
});

// Serve documentation
app.get('/docs/:filename', (req: Request, res: Response) => {
  const { filename } = req.params;
  const filePath = path.join(__dirname, '..', 'docs', filename);

  res.sendFile(filePath, (err) => {
    if (err) {
      console.error(`Error serving documentation file ${filename}:`, err);
      res.status(404).send('Documentation file not found');
    }
  });
});

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/tenants', tenantRoutes);
app.use('/api/users', userRoutes);
app.use('/api/services', serviceRoutes);
app.use('/api/availability', availabilityRoutes);
app.use('/api/appointments', appointmentRoutes);
app.use('/api/notifications', notificationRoutes);
app.use('/api/google-calendar', googleCalendarRoutes);
app.use('/api/super-admin', superAdminRoutes);
app.use('/api/subscriptions', subscriptionRoutes);
app.use('/api/razorpay', razorpayRoutes);
app.use('/api/usage', usageRoutes);
app.use('/api/admin/subscription', adminSubscriptionRoutes);

// API root health/status endpoint
app.get('/api', (req: Request, res: Response) => {
  res.send('Scheduly API root. See /api/auth, /api/super-admin, etc.');
});

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Start server
const startServer = async () => {
  try {
    // Test Supabase connection
    const isConnected = await testSupabaseConnection();
    if (!isConnected) {
      console.warn('Supabase connection failed. Some features may not work properly.');
      // Continue anyway for local development
    } else {
      console.log('Supabase connection established successfully.');
    }

    // Start server
    app.listen(port, () => {
      console.log(`Server is running on port ${port}`);
      console.log(`API available at http://localhost:${port}/api`);

      // Initialize the scheduler
      try {
        initializeScheduler();
      } catch (error) {
        console.warn('Failed to initialize scheduler:', error);
        console.warn('Scheduler functionality may not work properly.');
      }
    });
  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
};

startServer();

