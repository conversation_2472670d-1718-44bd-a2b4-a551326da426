import express, { Request, Response, NextFunction } from 'express';
import {
  getServices,
  getServiceById,
  createService,
  updateService,
  deleteService,
  assignStaffToService
} from '../controllers/serviceController';
import { authenticate, authorize, checkTenantAccess } from '../middleware/auth';
import { UserRole } from '../models/supabase';

const router = express.Router();

// Public routes
router.get('/:tenantId/services', getServices as any);
router.get('/:tenantId/services/:serviceId', getServiceById as any);

// Protected routes
router.post('/:tenantId/services', authenticate as any, authorize(UserRole.ADMIN) as any, checkTenantAccess as any, createService as any);
router.put('/:tenantId/services/:serviceId', authenticate as any, authorize(UserRole.ADMIN) as any, checkTenantAccess as any, updateService as any);
router.delete('/:tenantId/services/:serviceId', authenticate as any, authorize(UserRole.ADMIN) as any, checkTenantAccess as any, deleteService as any);
router.post('/:tenantId/services/:serviceId/staff', authenticate as any, authorize(UserRole.ADMIN) as any, checkTenantAccess as any, assignStaffToService as any);

export default router;
