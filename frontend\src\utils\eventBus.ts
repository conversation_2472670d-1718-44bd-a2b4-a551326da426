// Simple event bus for communication between components
type EventCallback = (...args: any[]) => void;

interface EventBus {
  events: Record<string, EventCallback[]>;
  on(event: string, callback: EventCallback): void;
  off(event: string, callback: EventCallback): void;
  emit(event: string, ...args: any[]): void;
}

const eventBus: EventBus = {
  events: {} as Record<string, EventCallback[]>,

  on(event: string, callback: EventCallback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  },

  off(event: string, callback: EventCallback) {
    if (this.events[event]) {
      this.events[event] = this.events[event].filter(cb => cb !== callback);
    }
  },

  emit(event: string, ...args: any[]) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(...args));
    }
  }
};

// Event types
export const EVENT_TYPES = {
  APPOINTMENT_CREATED: 'appointment:created',
  APPOINTMENT_UPDATED: 'appointment:updated',
  APPOINTMENT_DELETED: 'appointment:deleted',
  CLIENT_CREATED: 'client:created',
  CLIENT_UPDATED: 'client:updated',
  CLIENT_DELETED: 'client:deleted',
  SERVICE_CREATED: 'service:created',
  SERVICE_UPDATED: 'service:updated',
  SERVICE_DELETED: 'service:deleted',
  DATA_CHANGED: 'data:changed' // General event for any data change
};

export default eventBus;
