import { Appointment, User, Service, AppointmentStatus } from '../src/models';
import { createGoogleCalendarEvent } from '../src/utils/googleCalendar';
import { Op } from 'sequelize';

/**
 * Script to sync existing appointments to Google Calendar
 * This will find all confirmed appointments that don't have a Google Calendar event ID
 * and create events for them in Google Calendar
 */
const syncAppointmentsToGoogleCalendar = async () => {
  try {
    console.log('Starting appointment sync to Google Calendar...');

    // Find admin user with Google Calendar token
    const adminWithToken = await User.findOne({
      where: {
        role: 'admin',
        googleCalendarToken: { [Op.ne]: null }
      } as any
    });

    if (!adminWithToken) {
      console.log('No admin user with Google Calendar token found');
      return;
    }

    console.log(`Found admin user with Google Calendar token: ${adminWithToken.email}`);

    // Find all confirmed appointments without a Google Calendar event ID
    const appointments = await Appointment.findAll({
      where: {
        status: AppointmentStatus.CONFIRMED,
        googleEventId: null
      } as any,
      include: [
        { model: User, as: 'client' },
        { model: User, as: 'staff' },
        { model: Service, as: 'service' }
      ]
    });

    console.log(`Found ${appointments.length} appointments to sync`);

    if (appointments.length === 0) {
      console.log('No appointments to sync');
      return;
    }

    // Process each appointment
    for (const appointment of appointments) {
      // Use type assertion to access the associations
      const appointmentWithAssociations = appointment as any;
      const staff = appointmentWithAssociations.staff as User;
      const client = appointmentWithAssociations.client as User;
      const service = appointmentWithAssociations.service as Service;

      console.log(`Processing appointment ${appointment.id} for ${client.firstName} ${client.lastName}`);
      console.log(`Staff: ${staff.firstName} ${staff.lastName} (${staff.email})`);
      console.log(`Service: ${service.name}`);

      // Use admin user for Google Calendar integration
      const userWithToken = adminWithToken;

      console.log(`Using admin user ${userWithToken.email} for Google Calendar integration`);
      console.log(`Admin token:`, userWithToken.googleCalendarToken ? 'Present' : 'Missing');
      console.log(`Admin refresh token:`, userWithToken.googleCalendarRefreshToken ? 'Present' : 'Missing');

      try {
        // Create a modified version of the appointment with admin's token
        const modifiedStaff = {
          ...staff.toJSON(),
          googleCalendarToken: userWithToken.googleCalendarToken,
          googleCalendarRefreshToken: userWithToken.googleCalendarRefreshToken,
          googleCalendarTokenExpiry: userWithToken.googleCalendarTokenExpiry
        };

        // Create Google Calendar event
        const eventId = await createGoogleCalendarEvent(
          appointment,
          client,
          modifiedStaff as User,
          service
        );

        if (eventId) {
          // Update appointment with Google Calendar event ID
          await appointment.update({ googleEventId: eventId });
          console.log(`Successfully synced appointment ${appointment.id} to Google Calendar with event ID ${eventId}`);
        } else {
          console.log(`Failed to create Google Calendar event for appointment ${appointment.id}`);
        }
      } catch (error) {
        console.error(`Error syncing appointment ${appointment.id}:`, error);
        if (error instanceof Error) {
          console.error('Error message:', error.message);
        }
      }
    }

    console.log('Appointment sync completed');
  } catch (error) {
    console.error('Error syncing appointments to Google Calendar:', error);
  }
};

// Run the sync function
syncAppointmentsToGoogleCalendar()
  .then(() => {
    console.log('Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
