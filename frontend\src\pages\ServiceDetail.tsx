import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import Layout from '../components/Layout';
import { useAuth } from '../contexts/AuthContext';
import { useCurrency } from '../contexts/CurrencyContext';
import { serviceService, userService, UserRole } from '../services';

const ServiceDetail: React.FC = () => {
  const { serviceId } = useParams<{ serviceId: string }>();
  const { tenant, user } = useAuth();
  const { formatPrice } = useCurrency();
  const navigate = useNavigate();
  const [service, setService] = useState<any>(null);
  const [staff, setStaff] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    const fetchServiceAndStaff = async () => {
      if (!tenant || !serviceId) return;

      setIsLoading(true);
      setError(null);

      try {
        // Fetch service details
        const serviceData = await serviceService.getServiceById(tenant.id, serviceId);
        setService(serviceData);

        // Fetch staff members
        if (user?.role === UserRole.ADMIN) {
          const staffData = await userService.getUsers(tenant.id, UserRole.STAFF);
          setStaff(staffData);
        }
      } catch (err: any) {
        setError('Failed to load service details. Please try again later.');
        console.error('Error fetching service details:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchServiceAndStaff();
  }, [tenant, serviceId, user]);

  const handleDelete = async () => {
    if (!tenant || !serviceId) return;

    setIsDeleting(true);
    setError(null);

    try {
      await serviceService.deleteService(tenant.id, serviceId);
      navigate('/services');
    } catch (err: any) {
      setError(
        err.response?.data?.message || 'Failed to delete service. Please try again.'
      );
      console.error('Error deleting service:', err);
      setIsDeleting(false);
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes} minutes`;
    }
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0
      ? `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes} minute${
          remainingMinutes > 1 ? 's' : ''
        }`
      : `${hours} hour${hours > 1 ? 's' : ''}`;
  };

  return (
    <Layout>
      <div className="max-w-4xl mx-auto">
        {error && (
          <div className="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
            {error}
          </div>
        )}

        {isLoading ? (
          <div className="flex justify-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : service ? (
          <>
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-3xl font-bold text-gray-800">{service.name}</h1>
              {user?.role === UserRole.ADMIN && (
                <div className="flex space-x-2">
                  <Link
                    to={`/services/${serviceId}/edit`}
                    className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
                  >
                    Edit
                  </Link>
                  <button
                    onClick={() => setShowDeleteConfirm(true)}
                    className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
                  >
                    Delete
                  </button>
                </div>
              )}
            </div>

            <div className="bg-white rounded-lg shadow-md overflow-hidden">
              <div
                className="h-3"
                style={{ backgroundColor: service.color || '#4F46E5' }}
              ></div>
              <div className="p-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  <div>
                    <h2 className="text-lg font-semibold text-gray-800 mb-4">
                      Service Details
                    </h2>
                    <div className="space-y-3">
                      <div>
                        <span className="text-gray-600 font-medium">Duration:</span>{' '}
                        <span>{formatDuration(service.duration)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600 font-medium">Price:</span>{' '}
                        <span>{formatPrice(service.price)}</span>
                      </div>
                      <div>
                        <span className="text-gray-600 font-medium">Status:</span>{' '}
                        <span
                          className={`px-2 py-1 rounded-full text-xs font-medium ${
                            service.active
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}
                        >
                          {service.active ? 'Active' : 'Inactive'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h2 className="text-lg font-semibold text-gray-800 mb-4">
                      Description
                    </h2>
                    <p className="text-gray-600">
                      {service.description || 'No description provided'}
                    </p>
                  </div>
                </div>

                {service.staff && service.staff.length > 0 && (
                  <div className="mt-8">
                    <h2 className="text-lg font-semibold text-gray-800 mb-4">
                      Staff Providing This Service
                    </h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                      {service.staff.map((staffMember: any) => (
                        <div
                          key={staffMember.id}
                          className="bg-gray-50 p-4 rounded-md"
                        >
                          <p className="font-medium">
                            {staffMember.firstName} {staffMember.lastName}
                          </p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Delete Confirmation Modal */}
            {showDeleteConfirm && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg p-6 max-w-md w-full">
                  <h3 className="text-xl font-bold text-gray-800 mb-4">
                    Delete Service
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Are you sure you want to delete this service? This action cannot be
                    undone.
                  </p>
                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={() => setShowDeleteConfirm(false)}
                      className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleDelete}
                      disabled={isDeleting}
                      className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50"
                    >
                      {isDeleting ? 'Deleting...' : 'Delete'}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <p className="text-gray-500">Service not found</p>
            <Link
              to="/services"
              className="mt-4 inline-block text-blue-600 hover:text-blue-800"
            >
              Back to Services
            </Link>
          </div>
        )}
      </div>
    </Layout>
  );
};

export default ServiceDetail;
