import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';
import { tenantService } from '../services';
import { formatPrice, getCurrencySymbol } from '../utils/currencyUtils';

interface CurrencyContextType {
  currencyCode: string;
  setCurrencyCode: (code: string) => void;
  formatPrice: (price: number) => string;
  getCurrencySymbol: () => string;
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined);

export const CurrencyProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { tenant } = useAuth();
  const [currencyCode, setCurrencyCode] = useState<string>('INR');

  useEffect(() => {
    const fetchTenantCurrency = async () => {
      if (!tenant) return;

      try {
        const tenantData = await tenantService.getTenant(tenant.id);
        if (tenantData.currency) {
          setCurrencyCode(tenantData.currency);
        }
      } catch (error) {
        console.error('Error fetching tenant currency:', error);
      }
    };

    fetchTenantCurrency();
  }, [tenant]);

  const formatPriceWithCurrency = (price: number) => {
    return formatPrice(price, currencyCode);
  };

  const getCurrencySymbolForContext = () => {
    return getCurrencySymbol(currencyCode);
  };

  return (
    <CurrencyContext.Provider
      value={{
        currencyCode,
        setCurrencyCode,
        formatPrice: formatPriceWithCurrency,
        getCurrencySymbol: getCurrencySymbolForContext,
      }}
    >
      {children}
    </CurrencyContext.Provider>
  );
};

export const useCurrency = (): CurrencyContextType => {
  const context = useContext(CurrencyContext);
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider');
  }
  return context;
};
