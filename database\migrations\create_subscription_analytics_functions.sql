-- Function to count subscriptions by tier
CREATE OR REPLACE FUNCTION get_subscription_counts_by_tier(status_filter text DEFAULT NULL)
RETURNS TABLE (tier_id text, count bigint) AS $$
BEGIN
  IF status_filter IS NULL THEN
    RETURN QUERY
    SELECT s.tier_id::text, COUNT(s.id)::bigint
    FROM subscriptions s
    GROUP BY s.tier_id;
  ELSE
    RETURN QUERY
    SELECT s.tier_id::text, COUNT(s.id)::bigint
    FROM subscriptions s
    WHERE s.status = status_filter
    GROUP BY s.tier_id;
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Function to count subscriptions by status
CREATE OR REPLACE FUNCTION get_subscription_counts_by_status()
RETURNS TABLE (status text, count bigint) AS $$
BEGIN
  RETURN QUERY
  SELECT s.status::text, COUNT(s.id)::bigint
  FROM subscriptions s
  GROUP BY s.status;
END;
$$ LANGUAGE plpgsql;
