// This script checks the database directly using pg
// Run with: node src/scripts/check-db.js

const { Pool } = require('pg');
require('dotenv').config();

// Database connection
const pool = new Pool({
  user: process.env.DB_USER || 'postgres',
  host: process.env.DB_HOST || 'localhost',
  database: process.env.DB_NAME || 'appointy',
  password: process.env.DB_PASSWORD || 'postgres',
  port: process.env.DB_PORT || 5432,
});

async function checkDatabase() {
  const client = await pool.connect();
  
  try {
    console.log('Checking database...');
    
    // Check users table
    const usersQuery = `
      SELECT id, "tenantId", "firstName", "lastName", email, role, active, 
             CASE WHEN password IS NULL THEN false ELSE true END as "hasPassword"
      FROM users
      WHERE role = 'super_admin'
    `;
    
    const usersResult = await client.query(usersQuery);
    
    if (usersResult.rows.length > 0) {
      console.log('Super admin users found:', usersResult.rows);
    } else {
      console.log('No super admin users found');
    }
    
    // Check if the role is correct in the enum
    const enumQuery = `
      SELECT n.nspname as "schema",
             t.typname as "name",
             e.enumlabel as "value"
      FROM pg_type t
      JOIN pg_enum e ON t.oid = e.enumtypid
      JOIN pg_catalog.pg_namespace n ON n.oid = t.typnamespace
      WHERE t.typname = 'enum_users_role'
      ORDER BY e.enumsortorder;
    `;
    
    const enumResult = await client.query(enumQuery);
    
    if (enumResult.rows.length > 0) {
      console.log('User role enum values:', enumResult.rows.map(row => row.value));
    } else {
      console.log('No user role enum found');
    }
    
  } catch (error) {
    console.error('Error checking database:', error);
  } finally {
    client.release();
    pool.end();
  }
}

// Run the function
checkDatabase();
