import { User, Appointment, Notification } from '../src/models';
import { sequelize } from '../src/config/database';

const EMAIL_TO_REMOVE = '<EMAIL>';

async function removeUserAndRelatedData() {
  console.log(`Starting removal process for user with email: ${EMAIL_TO_REMOVE}`);
  
  try {
    // Start a transaction to ensure all operations succeed or fail together
    const transaction = await sequelize.transaction();
    
    try {
      // Find the user
      const user = await User.findOne({ 
        where: { email: EMAIL_TO_REMOVE },
        transaction
      });
      
      if (!user) {
        console.log('User not found. Nothing to remove.');
        await transaction.rollback();
        return;
      }
      
      const userId = user.id;
      console.log(`Found user with ID: ${userId}`);
      
      // Find all appointments for this user
      const appointments = await Appointment.findAll({
        where: { clientId: userId },
        transaction
      });
      
      console.log(`Found ${appointments.length} appointments for this user.`);
      
      // Get all appointment IDs
      const appointmentIds = appointments.map(appointment => appointment.id);
      
      // Delete all notifications related to these appointments
      if (appointmentIds.length > 0) {
        const deletedNotifications = await Notification.destroy({
          where: { 
            appointmentId: appointmentIds 
          },
          transaction
        });
        
        console.log(`Deleted ${deletedNotifications} notifications related to appointments.`);
      }
      
      // Delete all notifications related to this user
      const deletedUserNotifications = await Notification.destroy({
        where: { 
          userId 
        },
        transaction
      });
      
      console.log(`Deleted ${deletedUserNotifications} notifications related to the user.`);
      
      // Delete all appointments
      const deletedAppointments = await Appointment.destroy({
        where: { 
          clientId: userId 
        },
        transaction
      });
      
      console.log(`Deleted ${deletedAppointments} appointments.`);
      
      // Finally, delete the user
      await user.destroy({ transaction });
      
      console.log(`Deleted user with email: ${EMAIL_TO_REMOVE}`);
      
      // Commit the transaction
      await transaction.commit();
      console.log('All operations completed successfully.');
      
    } catch (error) {
      // If any operation fails, roll back the transaction
      await transaction.rollback();
      console.error('Transaction rolled back due to error:', error);
      throw error;
    }
    
  } catch (error) {
    console.error('Error in removal process:', error);
  } finally {
    // Close the database connection
    await sequelize.close();
    console.log('Database connection closed.');
  }
}

// Execute the function
removeUserAndRelatedData()
  .then(() => {
    console.log('Script execution completed.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script execution failed:', error);
    process.exit(1);
  });
