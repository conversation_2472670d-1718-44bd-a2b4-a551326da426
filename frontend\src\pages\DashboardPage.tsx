import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import UsageLimits from '../components/dashboard/UsageLimits';

const DashboardPage: React.FC = () => {
  const { user } = useAuth();

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="mb-6 text-2xl font-bold">Dashboard</h1>
      
      <div className="mb-8">
        <h2 className="mb-4 text-xl font-semibold">Welcome, {user?.first_name}!</h2>
        <p className="text-gray-600">
          Here's an overview of your account and usage statistics.
        </p>
      </div>
      
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Usage Limits Card */}
        <UsageLimits />
        
        {/* Recent Appointments Card */}
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
          <h2 className="mb-4 text-lg font-semibold">Recent Appointments</h2>
          <p className="text-gray-500">No recent appointments found.</p>
        </div>
        
        {/* Quick Actions Card */}
        <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
          <h2 className="mb-4 text-lg font-semibold">Quick Actions</h2>
          <div className="space-y-2">
            <a
              href="/appointments/new"
              className="block rounded-md bg-blue-600 px-4 py-2 text-center text-sm font-medium text-white hover:bg-blue-700"
            >
              Create Appointment
            </a>
            <a
              href="/staff/new"
              className="block rounded-md bg-white px-4 py-2 text-center text-sm font-medium text-blue-600 ring-1 ring-blue-600 hover:bg-blue-50"
            >
              Add Staff Member
            </a>
            <a
              href="/services"
              className="block rounded-md bg-white px-4 py-2 text-center text-sm font-medium text-blue-600 ring-1 ring-blue-600 hover:bg-blue-50"
            >
              Manage Services
            </a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
