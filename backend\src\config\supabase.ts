import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
import config from './index';

dotenv.config();

// For server-side operations, we should use the service role key
const supabaseUrl = config.supabase.url;
const supabaseKey = config.supabase.serviceKey;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or service role key. Please check your environment variables.');
  console.error('Required environment variables: SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

console.log('Initializing Supabase client with service role key');

// Create Supabase client with automatic retries and timeout
export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true
  },
  db: {
    schema: 'public'
  },
  global: {
    headers: { 'x-application-name': 'scheduly-backend' }
  }
});

// Test Supabase connection with retries
export const testConnection = async (retries = 3, delay = 2000) => {
  for (let i = 0; i < retries; i++) {
    try {
      const { data, error } = await supabase.from('tenants').select('id').limit(1);

      if (error) {
        // If it's an RLS error, that's actually good - it means we're connected
        if (error.message.includes('policy') || error.message.includes('permission')) {
          console.log('Successfully connected to Supabase (with expected RLS restrictions)');
          return true;
        }
        throw error;
      }

      console.log('Successfully connected to Supabase');
      return true;
    } catch (error) {
      console.error(`Supabase connection attempt ${i + 1}/${retries} failed:`, error);
      if (i < retries - 1) {
        console.log(`Retrying in ${delay/1000} seconds...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  console.error('Failed to connect to Supabase after multiple attempts');
  return false;
};

// Helper function to convert Supabase data to camelCase
export const toCamelCase = (obj: any): any => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(toCamelCase);
  }

  return Object.keys(obj).reduce((acc: any, key) => {
    const camelKey = key.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
    acc[camelKey] = toCamelCase(obj[key]);
    return acc;
  }, {});
};

// Helper function to convert camelCase to snake_case for Supabase
export const toSnakeCase = (obj: any): any => {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(toSnakeCase);
  }

  return Object.keys(obj).reduce((acc: any, key) => {
    const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    acc[snakeKey] = toSnakeCase(obj[key]);
    return acc;
  }, {});
};
