import { Request, Response } from 'express';
import {
  AppointmentStatus,
  UserRole,
  DayOfWeek,
  NotificationType,
  NotificationChannel,
  Appointment as AppointmentType,
  User as UserType,
  Service as ServiceType,
  Tenant as TenantType
} from '../models/supabase';
import {
  Appointment,
  User,
  Service,
  Tenant,
  Availability,
  TimeOff,
  Notification
} from '../utils/modelHelpers';
import {
  sendAppointmentConfirmation as sendLegacyEmail
} from '../utils/email';
import {
  sendAppointmentConfirmation as sendEmail
} from '../utils/resendEmail';
import {
  sendAppointmentConfirmationSMS as sendSMS
} from '../utils/sms';
import {
  createGoogleCalendarEvent,
  updateGoogleCalendarEvent,
  deleteGoogleCalendarEvent
} from '../utils/googleCalendar';
import { Op } from '../utils/queryOperators';
import { QueryOptions } from '../utils/modelHelpers';
import { canCreateAppointment } from '../services/appointmentService';


// Helper function to check staff availability
const isStaffAvailable = async (
  staffId: string,
  startTime: Date,
  endTime: Date
): Promise<boolean> => {
  // Get day of week
  const dayOfWeek = startTime.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase() as DayOfWeek;

  // Get time in HH:MM format
  const startTimeStr = startTime.toLocaleTimeString('en-US', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  });

  const endTimeStr = endTime.toLocaleTimeString('en-US', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit'
  });

  // Check regular availability
  const availability = await Availability.findOne({
    where: {
      staffId, // This will be converted to staff_id in the query
      dayOfWeek, // This will be converted to day_of_week in the query
      isAvailable: true, // This will be converted to is_available in the query
      startTime: { // This will be converted to start_time in the query
        [Op.lte]: startTimeStr,
      },
      endTime: { // This will be converted to end_time in the query
        [Op.gte]: endTimeStr,
      },
    },
  });

  if (!availability) {
    return false;
  }

  // Check time off
  const timeOff = await TimeOff.findOne({
    where: {
      staffId, // This will be converted to staff_id in the query
      startDate: { // This will be converted to start_date in the query
        [Op.lte]: endTime,
      },
      endDate: { // This will be converted to end_date in the query
        [Op.gte]: startTime,
      },
    },
  });

  if (timeOff) {
    return false;
  }

  // Check existing appointments
  const existingAppointment = await Appointment.findOne({
    where: {
      staffId, // This will be converted to staff_id in the query
      status: {
        [Op.notIn]: [AppointmentStatus.CANCELLED],
      },
      [Op.or]: [
        {
          startTime: { // This will be converted to start_time in the query
            [Op.lt]: endTime,
          },
          endTime: { // This will be converted to end_time in the query
            [Op.gt]: startTime,
          },
        },
      ],
    },
  });

  return !existingAppointment;
};

// Get appointments for a tenant
export const getAppointments = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId } = req.params;
    const {
      startDate,
      endDate,
      staffId,
      clientId,
      status
    } = req.query;

    // Check if user has access to this tenant
    if (req.user.tenantId !== tenantId && req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to access appointments for this tenant' });
    }

    // Build query
    const query: any = {
      where: {
        tenantId,
      },
      include: [
        {
          model: User,
          as: 'client',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone'],
        },
        {
          model: User,
          as: 'staff',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone'],
        },
        {
          model: Service,
          as: 'service',
        },
      ],
      order: [['startTime', 'ASC']],
    };

    // Filter by date range
    if (startDate && endDate) {
      query.where.startTime = {
        [Op.gte]: new Date(startDate as string),
      };
      query.where.endTime = {
        [Op.lte]: new Date(endDate as string),
      };
    }

    // Filter by staff
    if (staffId) {
      query.where.staffId = staffId;
    }

    // Filter by client
    if (clientId) {
      query.where.clientId = clientId;
    }

    // Filter by status
    if (status) {
      query.where.status = status;
    }

    // If user is a client, only show their appointments
    if (req.user.role === UserRole.CLIENT) {
      query.where.clientId = req.user.id;
    }

    // If user is a staff, only show their appointments
    if (req.user.role === UserRole.STAFF) {
      query.where.staffId = req.user.id;
    }

    const appointments = await Appointment.findAll(query);

    res.json(appointments);
  } catch (error) {
    console.error('Get appointments error:', error);
    res.status(500).json({ message: 'Failed to get appointments' });
  }
};

// Get appointment by ID
export const getAppointmentById = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId, appointmentId } = req.params;

    // Check if user has access to this tenant
    if (req.user.tenantId !== tenantId && req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to access appointments for this tenant' });
    }

    const appointment = await Appointment.findOne({
      where: {
        id: appointmentId,
        tenantId,
      },
      include: [
        {
          model: User,
          as: 'client',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone'],
        },
        {
          model: User,
          as: 'staff',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone'],
        },
        {
          model: Service,
          as: 'service',
        },
        {
          model: Tenant,
          as: 'tenant',
          attributes: ['id', 'name', 'email', 'phone', 'logo', 'primaryColor', 'secondaryColor'],
        },
      ],
    });

    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found' });
    }

    // If user is a client, only allow access to their own appointments
    if (req.user.role === UserRole.CLIENT && appointment.clientId !== req.user.id) {
      return res.status(403).json({ message: 'Not authorized to access this appointment' });
    }

    // If user is a staff, only allow access to their own appointments
    if (req.user.role === UserRole.STAFF && appointment.staffId !== req.user.id) {
      return res.status(403).json({ message: 'Not authorized to access this appointment' });
    }

    res.json(appointment);
  } catch (error) {
    console.error('Get appointment error:', error);
    res.status(500).json({ message: 'Failed to get appointment' });
  }
};

// Create a new appointment
export const createAppointment = async (req: Request, res: Response) => {
  try {
    // Subscription/usage check (consolidated logic)
    const userId = req.user?.id;
    if (!userId) {
      return res.status(401).json({ message: 'Authentication required' });
    }
    const canCreate = await canCreateAppointment(userId);
    if (!canCreate.canCreate) {
      return res.status(403).json({ message: canCreate.reason || 'You have reached your appointment limit', limit: canCreate.limit, current: canCreate.current, upgradeRequired: true });
    }

    const { tenantId } = req.params;
    const {
      clientId,
      staffId,
      serviceId,
      startTime,
      notes
    } = req.body;

    // Check if tenant exists
    const tenant = await Tenant.findByPk(tenantId);

    if (!tenant) {
      return res.status(404).json({ message: 'Tenant not found' });
    }

    if (!tenant.active) {
      return res.status(403).json({ message: 'This business is not currently accepting bookings' });
    }

    // Check if client exists
    const client = await User.findOne({
      where: {
        id: clientId,
        tenantId,
        active: true,
      },
    });

    if (!client) {
      return res.status(404).json({ message: 'Client not found' });
    }

    // Check if staff exists
    const staff = await User.findOne({
      where: {
        id: staffId,
        tenantId,
        role: [UserRole.ADMIN, UserRole.STAFF],
        active: true,
      },
    });

    if (!staff) {
      return res.status(404).json({ message: 'Staff member not found' });
    }

    // Check if service exists
    const service = await Service.findOne({
      where: {
        id: serviceId,
        tenantId,
        active: true,
      },
    });

    if (!service) {
      return res.status(404).json({ message: 'Service not found' });
    }

    // Parse start time - ensure we're using the ISO string directly
    // This preserves the exact time that was selected by the user
    console.log('Received startTime:', startTime);
    const start = new Date(startTime);

    if (isNaN(start.getTime())) {
      return res.status(400).json({ message: 'Invalid start time format' });
    }

    console.log('Parsed start time:', start.toISOString());

    // Calculate end time based on service duration
    const end = new Date(start.getTime() + service.duration * 60000);
    console.log('Calculated end time:', end.toISOString());

    // Check if start time is in the future
    if (start <= new Date()) {
      return res.status(400).json({ message: 'Appointment must be scheduled in the future' });
    }

    // Check if staff is available
    const isAvailable = await isStaffAvailable(staffId, start, end);

    if (!isAvailable) {
      return res.status(400).json({ message: 'Staff is not available at the selected time' });
    }

    // Create appointment
    const appointment = await Appointment.create({
      tenantId,
      clientId,
      staffId,
      serviceId,
      startTime: start,
      endTime: end,
      status: AppointmentStatus.CONFIRMED,
      notes,
    });

    if (!appointment) {
      return res.status(500).json({ message: 'Failed to create appointment' });
    }

    // Create Google Calendar event if staff has connected their calendar
    // or if an admin has connected their calendar
    try {
      let userWithToken = null;

      if (staff.googleCalendarToken) {
        console.log('Staff has Google Calendar token, creating event...');
        userWithToken = staff;
      } else {
        // Try to find an admin with Google Calendar token
        const admin = await User.findOne({
          where: {
            tenantId,
            role: UserRole.ADMIN,
            googleCalendarToken: { [Op.not]: null ,} as any,
            active: true
          }
        });

        if (admin) {
          console.log('Using admin Google Calendar token for staff without token');
          userWithToken = admin;
        }
      }

      if (userWithToken) {
        console.log('User token:', userWithToken.googleCalendarToken ? 'Present' : 'Missing');
        console.log('User refresh token:', userWithToken.googleCalendarRefreshToken ? 'Present' : 'Missing');

        // Create a modified version of the staff with the token if needed
        const staffWithToken = userWithToken.id === staff.id ? staff : {
          ...staff,
          googleCalendarToken: userWithToken.googleCalendarToken,
          googleCalendarRefreshToken: userWithToken.googleCalendarRefreshToken,
          googleCalendarTokenExpiry: userWithToken.googleCalendarTokenExpiry
        };

        const eventId = await createGoogleCalendarEvent(
          appointment,
          client,
          staffWithToken as UserType,
          service
        );

        if (eventId) {
          console.log('Google Calendar event created, updating appointment with event ID:', eventId);
          await Appointment.update(
            { googleEventId: eventId },
            {
              where: {
                id: appointment.id
              }
            }
          );
        } else {
          console.log('No Google Calendar event ID returned');
        }
      } else {
        console.log('No user with Google Calendar token found, skipping calendar integration');
      }
    } catch (error) {
      console.error('Failed to create Google Calendar event:', error);
      if (error instanceof Error) {
        console.error('Error message:', error.message);
      }
    }

    // Send confirmation notifications
    try {
      // Email notification
      const emailSent = await sendEmail(appointment, client, staff, service, tenant);

      // Create notification record
      await Notification.create({
        userId: client.id,
        appointmentId: appointment.id,
        type: NotificationType.APPOINTMENT_CREATED,
        channel: NotificationChannel.EMAIL,
        content: `Appointment confirmation for ${service.name} with ${staff.firstName} ${staff.lastName} on ${start.toLocaleDateString()} at ${start.toLocaleTimeString()}`,
        sent: emailSent,
        sentAt: emailSent ? new Date() : undefined,
        error: emailSent ? undefined : 'Failed to send email',
      });

      // SMS notification if client has phone
      if (client.phone) {
        const smsSent = await sendSMS(appointment, client, staff, service, tenant);

        // Create notification record
        await Notification.create({
          userId: client.id,
          appointmentId: appointment.id,
          type: NotificationType.APPOINTMENT_CREATED,
          channel: NotificationChannel.SMS,
          content: `Appointment confirmation for ${service.name} with ${staff.firstName} ${staff.lastName} on ${start.toLocaleDateString()} at ${start.toLocaleTimeString()}`,
          sent: smsSent,
          sentAt: smsSent ? new Date() : undefined,
          error: smsSent ? undefined : 'Failed to send SMS',
        });
      }
    } catch (error) {
      console.error('Failed to send confirmation notifications:', error);
    }

    // Get the complete appointment with associations
    const createdAppointment = await Appointment.findOne({
      where: {
        id: appointment.id
      },
      include: [
        {
          model: User,
          as: 'client'
        },
        {
          model: User,
          as: 'staff'
        }
      ]
    });

    res.status(201).json({
      message: 'Appointment created successfully',
      appointment: createdAppointment
    });
  } catch (error) {
    console.error('Create appointment error:', error);
    res.status(500).json({ message: 'Failed to create appointment' });
  }
};

// Update an appointment
export const updateAppointment = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId, appointmentId } = req.params;
    const {
      staffId,
      serviceId,
      startTime,
      status,
      notes
    } = req.body;

    // Check if user has access to this tenant
    if (req.user.tenantId !== tenantId && req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to update appointments for this tenant' });
    }

    // Get appointment
    const appointment = await Appointment.findOne({
      where: {
        id: appointmentId,
        tenantId,
      },
      include: [
        {
          model: User,
          as: 'client',
        },
        {
          model: User,
          as: 'staff',
        },
        {
          model: Service,
          as: 'service',
        },
        {
          model: Tenant,
          as: 'tenant',
        },
      ],
    });

    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found' });
    }

    // If user is a client, only allow updating their own appointments
    if (req.user.role === UserRole.CLIENT) {
      if (appointment.clientId !== req.user.id) {
        return res.status(403).json({ message: 'Not authorized to update this appointment' });
      }

      // Clients can only cancel appointments
      if (status && status !== AppointmentStatus.CANCELLED) {
        return res.status(403).json({ message: 'Clients can only cancel appointments' });
      }
    }

    // If user is a staff, only allow updating their own appointments
    if (req.user.role === UserRole.STAFF) {
      if (appointment.staffId !== req.user.id) {
        return res.status(403).json({ message: 'Not authorized to update this appointment' });
      }
    }

    // Build update object
    const updateData: any = {};
    let newStartTime: Date | null = null;
    let newEndTime: Date | null = null;
    let newService: ServiceType | null = null;
    let newStaff: UserType | null = null;

    // Update staff if provided
    if (staffId && staffId !== appointment.staffId) {
      // Check if staff exists
      newStaff = await User.findOne({
        where: {
          id: staffId,
          tenantId,
          role: [UserRole.ADMIN, UserRole.STAFF],
          active: true,
        },
      });

      if (!newStaff) {
        return res.status(404).json({ message: 'Staff member not found' });
      }

      updateData.staffId = staffId;
    }

    // Update service if provided
    if (serviceId && serviceId !== appointment.serviceId) {
      // Check if service exists
      newService = await Service.findOne({
        where: {
          id: serviceId,
          tenantId,
          active: true,
        },
      });

      if (!newService) {
        return res.status(404).json({ message: 'Service not found' });
      }

      updateData.serviceId = serviceId;
    }

    // Update start time if provided
    if (startTime) {
      newStartTime = new Date(startTime);

      if (isNaN(newStartTime.getTime())) {
        return res.status(400).json({ message: 'Invalid start time format' });
      }

      // Calculate new end time
      // Use type assertion to access the service association
      const appointmentWithService = appointment as any;
      const serviceDuration = newService ? newService.duration : appointmentWithService.service.duration;
      newEndTime = new Date(newStartTime.getTime() + serviceDuration * 60000);

      // Check if start time is in the future
      if (newStartTime <= new Date()) {
        return res.status(400).json({ message: 'Appointment must be scheduled in the future' });
      }

      updateData.startTime = newStartTime;
      updateData.endTime = newEndTime;
    }

    // Check staff availability if time or staff changed
    if ((newStartTime || newEndTime) || newStaff) {
      const checkStaffId = newStaff ? newStaff.id : appointment.staffId;
      const checkStartTime = newStartTime || appointment.startTime;
      const checkEndTime = newEndTime || appointment.endTime;

      // Skip availability check for cancelled appointments
      if (status !== AppointmentStatus.CANCELLED) {
        const isAvailable = await isStaffAvailable(
          checkStaffId,
          checkStartTime,
          checkEndTime
        );

        if (!isAvailable) {
          return res.status(400).json({ message: 'Staff is not available at the selected time' });
        }
      }
    }

    // Update status if provided
    if (status) {
      updateData.status = status;
    }

    // Update notes if provided
    if (notes !== undefined) {
      updateData.notes = notes;
    }

    // Update appointment
    await Appointment.update(
      updateData,
      {
        where: {
          id: appointmentId
        }
      }
    );

    // Update Google Calendar event
    if (appointment.googleEventId && (newStartTime || newEndTime || newService || newStaff || status === AppointmentStatus.CANCELLED)) {
      try {
        // Get the updated appointment with associations
        const updatedAppointment = await Appointment.findOne({
          where: {
            id: appointmentId
          },
          include: [
            {
              model: User,
              as: 'client'
            },
            {
              model: User,
              as: 'staff'
            },
            {
              model: Service,
              as: 'service'
            },
            {
              model: Tenant,
              as: 'tenant'
            }
          ]
        });

        // Make sure updatedAppointment is not null and handle staff relationship properly
        if (!updatedAppointment) {
          console.error('Failed to fetch updated appointment');
          return res.status(500).json({ message: 'Failed to update appointment' });
        }

        // Get staff from the relationship or use newStaff
        const staffId = updatedAppointment.staffId;
        const staff = newStaff || await User.findByPk(staffId);

        // Check if staff was found
        if (!staff) {
          console.error('Failed to fetch staff data');
          return res.status(500).json({ message: 'Failed to update appointment' });
        }

        let userWithToken = null;

        if (staff.googleCalendarToken) {
          console.log('Staff has Google Calendar token, updating event...');
          userWithToken = staff;
        } else {
          // Try to find an admin with Google Calendar token
          const admin = await User.findOne({
            where: {
              tenantId,
              role: UserRole.ADMIN,
              googleCalendarToken: { [Op.not]: null ,} as any,
              active: true
            }
          });

          if (admin) {
            console.log('Using admin Google Calendar token for staff without token');
            userWithToken = admin;
          }
        }

        if (userWithToken) {
          console.log('User token:', userWithToken.googleCalendarToken ? 'Present' : 'Missing');
          console.log('User refresh token:', userWithToken.googleCalendarRefreshToken ? 'Present' : 'Missing');

          // Create a modified version of the staff with the token if needed
          const staffWithToken = userWithToken.id === staff.id ? staff : {
            ...staff,
            googleCalendarToken: userWithToken.googleCalendarToken || '',
            googleCalendarRefreshToken: userWithToken.googleCalendarRefreshToken,
            googleCalendarTokenExpiry: userWithToken.googleCalendarTokenExpiry
          };

          if (status === AppointmentStatus.CANCELLED) {
            // Make sure staffWithToken is not null and has required properties
            if (!staffWithToken || !staffWithToken.googleCalendarToken) {
              console.error('Missing required token information');
              return res.status(500).json({ message: 'Failed to update appointment' });
            }

            await deleteGoogleCalendarEvent(
              appointment.googleEventId,
              staffWithToken.googleCalendarToken,
              staffWithToken.googleCalendarRefreshToken
            );
          } else {
            // Get client and service data
            const clientId = updatedAppointment.clientId;
            const serviceId = updatedAppointment.serviceId;

            const client = await User.findByPk(clientId);
            const service = newService || await Service.findByPk(serviceId);

            if (!client || !service) {
              console.error('Failed to fetch client or service data');
              return res.status(500).json({ message: 'Failed to update appointment' });
            }

            await updateGoogleCalendarEvent(
              appointment.googleEventId,
              appointment,
              client,
              staffWithToken as any,
              service
            );
          }
        } else {
          console.log('No user with Google Calendar token found, skipping calendar integration');
        }
      } catch (error) {
        console.error('Failed to update Google Calendar event:', error);
        if (error instanceof Error) {
          console.error('Error message:', error.message);
        }
      }
    }

    // Send update notifications
    try {
      // Only send notifications for significant changes
      if (newStartTime || newEndTime || newService || newStaff || status === AppointmentStatus.CANCELLED) {
        // Get the updated appointment with associations if not already fetched
        const updatedAppointment = await Appointment.findOne({
          where: {
            id: appointmentId
          },
          include: [
            {
              model: User,
              as: 'client'
            },
            {
              model: User,
              as: 'staff'
            },
            {
              model: Service,
              as: 'service'
            },
            {
              model: Tenant,
              as: 'tenant'
            }
          ]
        });

        // Make sure updatedAppointment is not null
        if (!updatedAppointment) {
          console.error('Failed to fetch updated appointment');
          return res.status(500).json({ message: 'Failed to update appointment' });
        }

        // Get related data
        const clientId = updatedAppointment.clientId;
        const staffId = updatedAppointment.staffId;
        const serviceId = updatedAppointment.serviceId;
        const tenantId = updatedAppointment.tenantId;

        const client = await User.findByPk(clientId);
        const staffMember = newStaff || await User.findByPk(staffId);
        const service = newService || await Service.findByPk(serviceId);
        const tenant = await Tenant.findByPk(tenantId);

        if (!client || !staffMember || !service || !tenant) {
          console.error('Failed to fetch related data for notification');
          return res.status(500).json({ message: 'Failed to update appointment' });
        }

        // Email notification
        const emailSent = await sendEmail(
          appointment,
          client,
          staffMember,
          service,
          tenant
        );

        // Create notification record
        await Notification.create({
          userId: client.id,
          appointmentId: appointment.id,
          type: status === AppointmentStatus.CANCELLED
            ? NotificationType.APPOINTMENT_CANCELLED
            : NotificationType.APPOINTMENT_UPDATED,
          channel: NotificationChannel.EMAIL,
          content: `Appointment ${status === AppointmentStatus.CANCELLED ? 'cancellation' : 'update'} for ${service.name} with ${staffMember.firstName} ${staffMember.lastName} on ${(newStartTime || appointment.startTime).toLocaleDateString()} at ${(newStartTime || appointment.startTime).toLocaleTimeString()}`,
          sent: emailSent,
          sentAt: emailSent ? new Date() : undefined,
          error: emailSent ? undefined : 'Failed to send email',
        });

        // SMS notification if client has phone
        if (client.phone) {
          const smsSent = await sendSMS(
            appointment,
            client,
            staffMember,
            service,
            tenant
          );

          // Create notification record
          await Notification.create({
            userId: client.id,
            appointmentId: appointment.id,
            type: status === AppointmentStatus.CANCELLED
              ? NotificationType.APPOINTMENT_CANCELLED
              : NotificationType.APPOINTMENT_UPDATED,
            channel: NotificationChannel.SMS,
            content: `Appointment ${status === AppointmentStatus.CANCELLED ? 'cancellation' : 'update'} for ${service.name} with ${staffMember.firstName} ${staffMember.lastName} on ${(newStartTime || appointment.startTime).toLocaleDateString()} at ${(newStartTime || appointment.startTime).toLocaleTimeString()}`,
            sent: smsSent,
            sentAt: smsSent ? new Date() : undefined,
            error: smsSent ? undefined : 'Failed to send SMS',
          });
        }
      }
    } catch (error) {
      console.error('Failed to send update notifications:', error);
    }

    // Get the updated appointment with associations
    const updatedAppointment = await Appointment.findOne({
      where: { id: appointmentId },
      include: [
        {
          model: User,
          as: 'client',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone'],
        },
        {
          model: User,
          as: 'staff',
          attributes: ['id', 'firstName', 'lastName', 'email', 'phone'],
        },
        {
          model: Service,
          as: 'service',
        },
      ],
    });

    res.json({
      message: 'Appointment updated successfully',
      appointment: updatedAppointment,
    });
  } catch (error) {
    console.error('Update appointment error:', error);
    res.status(500).json({ message: 'Failed to update appointment' });
  }
};

// Delete an appointment
export const deleteAppointment = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const { tenantId, appointmentId } = req.params;

    // Check if user has admin access to this tenant
    if (req.user.tenantId !== tenantId || req.user.role !== UserRole.ADMIN) {
      return res.status(403).json({ message: 'Not authorized to delete appointments for this tenant' });
    }

    const appointment = await Appointment.findOne({
      where: {
        id: appointmentId,
        tenantId,
      },
    });

    if (!appointment) {
      return res.status(404).json({ message: 'Appointment not found' });
    }

    // Delete Google Calendar event
    if (appointment.googleEventId) {
      try {
        // Get staff details to get the Google Calendar token
        const staff = await User.findByPk(appointment.staffId);

        let userWithToken = null;

        if (staff && staff.googleCalendarToken) {
          console.log('Staff has Google Calendar token, deleting event...');
          userWithToken = staff;
        } else {
          // Try to find an admin with Google Calendar token
          const admin = await User.findOne({
            where: {
              tenantId,
              role: UserRole.ADMIN,
              googleCalendarToken: { [Op.not]: null ,} as any,
              active: true
            }
          });

          if (admin) {
            console.log('Using admin Google Calendar token for staff without token');
            userWithToken = admin;
          }
        }

        if (userWithToken) {
          console.log('User token:', userWithToken.googleCalendarToken ? 'Present' : 'Missing');
          console.log('User refresh token:', userWithToken.googleCalendarRefreshToken ? 'Present' : 'Missing');

          await deleteGoogleCalendarEvent(
            appointment.googleEventId,
            userWithToken.googleCalendarToken || '',
            userWithToken.googleCalendarRefreshToken
          );
        } else {
          console.log('No user with Google Calendar token found, skipping calendar integration');
        }
      } catch (error) {
        console.error('Failed to delete Google Calendar event:', error);
        if (error instanceof Error) {
          console.error('Error message:', error.message);
        }
      }
    }

    // Delete appointment
    await Appointment.destroy({
      where: {
        id: appointmentId
      }
    });

    res.json({ message: 'Appointment deleted successfully' });
  } catch (error) {
    console.error('Delete appointment error:', error);
    res.status(500).json({ message: 'Failed to delete appointment' });
  }
};

// Get available time slots for a service and staff
export const getAvailableTimeSlots = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;
    const {
      serviceId,
      staffId,
      date,
      excludeAppointmentId
    } = req.query;

    if (!serviceId || !staffId || !date) {
      return res.status(400).json({ message: 'Service ID, staff ID, and date are required' });
    }

    // Check if service exists
    const service = await Service.findOne({
      where: {
        id: serviceId as string,
        tenantId,
        active: true,
      },
    });

    if (!service) {
      return res.status(404).json({ message: 'Service not found' });
    }

    // Check if staff exists
    const staff = await User.findOne({
      where: {
        id: staffId as string,
        tenantId,
        role: [UserRole.ADMIN, UserRole.STAFF],
        active: true,
      },
    });

    if (!staff) {
      return res.status(404).json({ message: 'Staff member not found' });
    }

    // Parse date
    const selectedDate = new Date(date as string);

    if (isNaN(selectedDate.getTime())) {
      return res.status(400).json({ message: 'Invalid date format' });
    }

    // Get day of week
    const dayOfWeek = selectedDate.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase() as DayOfWeek;

    // Get staff availability for the day
    const availability = await Availability.findOne({
      where: {
        staffId: staffId as string,
        dayOfWeek,
        isAvailable: true,
      },
    });

    if (!availability) {
      return res.json({ timeSlots: [] });
    }

    // Check if staff has time off on this day
    const timeOff = await TimeOff.findOne({
      where: {
        staffId: staffId as string,
        startDate: {
          [Op.lte]: new Date(selectedDate.setHours(23, 59, 59)),
        },
        endDate: {
          [Op.gte]: new Date(selectedDate.setHours(0, 0, 0)),
        },
      },
    });

    if (timeOff) {
      return res.json({ timeSlots: [] });
    }

    // Get existing appointments for the day
    const whereClause: any = {
      staffId: staffId as string,
      status: {
        [Op.notIn]: [AppointmentStatus.CANCELLED],
      },
      startTime: {
        [Op.gte]: new Date(selectedDate.setHours(0, 0, 0)),
      },
      endTime: {
        [Op.lte]: new Date(selectedDate.setHours(23, 59, 59)),
      },
    };

    // Exclude the specified appointment if provided
    if (excludeAppointmentId) {
      whereClause.id = {
        [Op.ne]: excludeAppointmentId
      ,};
    }

    const existingAppointments = await Appointment.findAll({
      where: whereClause,
      order: [['startTime', 'ASC']],
    });

    // Parse availability times
    const [startHour, startMinute] = availability.startTime.split(':').map(Number);
    const [endHour, endMinute] = availability.endTime.split(':').map(Number);

    // Set start and end times for the day
    const dayStart = new Date(selectedDate);
    dayStart.setHours(startHour, startMinute, 0, 0);

    const dayEnd = new Date(selectedDate);
    dayEnd.setHours(endHour, endMinute, 0, 0);

    // Service duration in minutes
    const serviceDuration = service.duration;

    // Generate time slots (30-minute intervals)
    const timeSlots = [];
    const slotInterval = 30; // minutes
    let currentSlot = new Date(dayStart);

    while (currentSlot.getTime() + serviceDuration * 60000 <= dayEnd.getTime()) {
      const slotEnd = new Date(currentSlot.getTime() + serviceDuration * 60000);

      // Check if slot overlaps with any existing appointment
      const isOverlapping = existingAppointments.some((appointment: AppointmentType) => {
        return (
          (currentSlot < new Date(appointment.endTime) && slotEnd > new Date(appointment.startTime))
        );
      });

      if (!isOverlapping) {
        // Ensure we're using ISO strings for consistent timezone handling
        const startIso = currentSlot.toISOString();
        const endIso = slotEnd.toISOString();

        timeSlots.push({
          startTime: startIso,
          endTime: endIso,
        });

        console.log(`Generated time slot: ${startIso} - ${endIso}`);
      }

      // Move to next slot
      currentSlot = new Date(currentSlot.getTime() + slotInterval * 60000);
    }

    res.json({ timeSlots });
  } catch (error) {
    console.error('Get available time slots error:', error);
    res.status(500).json({ message: 'Failed to get available time slots' });
  }
};
