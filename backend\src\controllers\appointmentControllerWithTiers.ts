import { Request, Response } from 'express';
import { 
  getMonthlyAppointmentCount, 
  canCreateAppointment,
  getAppointmentById,
  createAppointment,
  updateAppointment,
  deleteAppointment
} from '../services/appointmentService';
import { userHasFeature } from '../services/subscriptionService';
import { TierLevel } from '../models/SubscriptionTier';

// Create a new appointment with tier checks
export const createAppointmentWithTierCheck = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({ 
        success: false, 
        message: 'Authentication required' 
      });
    }
    
    // Check if user can create more appointments based on their tier
    const canCreate = await canCreateAppointment(userId);
    
    if (!canCreate.canCreate) {
      return res.status(403).json({ 
        success: false, 
        message: canCreate.reason,
        limit: canCreate.limit,
        current: canCreate.current,
        upgradeRequired: true
      });
    }
    
    // Check if user has access to advanced calendar features if needed
    const needsAdvancedCalendar = req.body.customDuration || req.body.bufferTime;
    
    if (needsAdvancedCalendar) {
      const hasAdvancedCalendar = await userHasFeature(userId, 'advancedCalendarManagement');
      
      if (!hasAdvancedCalendar) {
        return res.status(403).json({ 
          success: false, 
          message: 'Advanced calendar features require a Pro or Pro+ subscription',
          upgradeRequired: true,
          requiredFeature: 'advancedCalendarManagement'
        });
      }
    }
    
    // Proceed with appointment creation
    const appointmentData = {
      tenant_id: tenantId,
      client_id: req.body.clientId,
      staff_id: req.body.staffId,
      service_id: req.body.serviceId,
      start_time: new Date(req.body.startTime),
      end_time: new Date(req.body.endTime || new Date(new Date(req.body.startTime).getTime() + req.body.duration * 60000)),
      status: req.body.status || 'confirmed',
      notes: req.body.notes,
      created_by: userId
    };
    
    const appointment = await createAppointment(appointmentData);
    
    if (!appointment) {
      return res.status(500).json({ 
        success: false, 
        message: 'Failed to create appointment' 
      });
    }
    
    // Get the complete appointment with associations
    const createdAppointment = await getAppointmentById(appointment.id);
    
    return res.status(201).json({
      success: true,
      message: 'Appointment created successfully',
      data: createdAppointment
    });
  } catch (error) {
    console.error('Error in createAppointmentWithTierCheck:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
};

// Update an appointment with tier checks
export const updateAppointmentWithTierCheck = async (req: Request, res: Response) => {
  try {
    const { tenantId, appointmentId } = req.params;
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({ 
        success: false, 
        message: 'Authentication required' 
      });
    }
    
    // Get the existing appointment
    const existingAppointment = await getAppointmentById(appointmentId);
    
    if (!existingAppointment) {
      return res.status(404).json({ 
        success: false, 
        message: 'Appointment not found' 
      });
    }
    
    // Check if user has access to advanced calendar features if needed
    const needsAdvancedCalendar = req.body.customDuration || req.body.bufferTime;
    
    if (needsAdvancedCalendar) {
      const hasAdvancedCalendar = await userHasFeature(userId, 'advancedCalendarManagement');
      
      if (!hasAdvancedCalendar) {
        return res.status(403).json({ 
          success: false, 
          message: 'Advanced calendar features require a Pro or Pro+ subscription',
          upgradeRequired: true,
          requiredFeature: 'advancedCalendarManagement'
        });
      }
    }
    
    // Prepare update data
    const updateData: any = {};
    
    if (req.body.staffId) updateData.staff_id = req.body.staffId;
    if (req.body.serviceId) updateData.service_id = req.body.serviceId;
    if (req.body.startTime) updateData.start_time = new Date(req.body.startTime);
    if (req.body.endTime) updateData.end_time = new Date(req.body.endTime);
    if (req.body.status) updateData.status = req.body.status;
    if (req.body.notes !== undefined) updateData.notes = req.body.notes;
    
    updateData.updated_at = new Date();
    updateData.updated_by = userId;
    
    // Update the appointment
    const updatedAppointment = await updateAppointment(appointmentId, updateData);
    
    if (!updatedAppointment) {
      return res.status(500).json({ 
        success: false, 
        message: 'Failed to update appointment' 
      });
    }
    
    // Get the complete updated appointment with associations
    const completeAppointment = await getAppointmentById(appointmentId);
    
    return res.status(200).json({
      success: true,
      message: 'Appointment updated successfully',
      data: completeAppointment
    });
  } catch (error) {
    console.error('Error in updateAppointmentWithTierCheck:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
};

// Get appointments with tier checks
export const getAppointmentsWithTierCheck = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;
    const userId = req.user?.id;
    
    if (!userId) {
      return res.status(401).json({ 
        success: false, 
        message: 'Authentication required' 
      });
    }
    
    // Check if user has access to advanced calendar features for advanced filtering
    const needsAdvancedCalendar = req.query.advancedFilters === 'true';
    
    if (needsAdvancedCalendar) {
      const hasAdvancedCalendar = await userHasFeature(userId, 'advancedCalendarManagement');
      
      if (!hasAdvancedCalendar) {
        return res.status(403).json({ 
          success: false, 
          message: 'Advanced calendar features require a Pro or Pro+ subscription',
          upgradeRequired: true,
          requiredFeature: 'advancedCalendarManagement'
        });
      }
    }
    
    // Build query
    const query: any = {
      tenant_id: tenantId
    };
    
    // Add filters
    if (req.query.startDate && req.query.endDate) {
      query.start_time = { gte: new Date(req.query.startDate as string).toISOString() };
      query.end_time = { lte: new Date(req.query.endDate as string).toISOString() };
    }
    
    if (req.query.staffId) {
      query.staff_id = req.query.staffId;
    }
    
    if (req.query.clientId) {
      query.client_id = req.query.clientId;
    }
    
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    // Execute query
    const { data: appointments, error } = await supabase
      .from('appointments')
      .select(`
        *,
        client:client_id(*),
        staff:staff_id(*),
        service:service_id(*)
      `)
      .match(query)
      .order('start_time', { ascending: true });
    
    if (error) {
      console.error('Error fetching appointments:', error);
      return res.status(500).json({ 
        success: false, 
        message: 'Failed to fetch appointments' 
      });
    }
    
    return res.status(200).json({
      success: true,
      data: appointments
    });
  } catch (error) {
    console.error('Error in getAppointmentsWithTierCheck:', error);
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    });
  }
};
