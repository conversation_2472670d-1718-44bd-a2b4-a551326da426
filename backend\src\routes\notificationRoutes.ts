import express, { Request, Response, NextFunction } from 'express';
import {
  getUserNotifications,
  markNotificationAsRead,
  sendAppointmentReminders,
  sendAllAppointmentReminders
} from '../controllers/notificationController';
import { authenticate, authorize, checkTenantAccess } from '../middleware/auth';
import { UserRole } from '../models/supabase';

const router = express.Router();

// All routes are protected
router.get('/:tenantId/users/:userId/notifications', authenticate as any, checkTenantAccess as any, getUserNotifications as any);
router.put('/:tenantId/notifications/:notificationId/read', authenticate as any, checkTenantAccess as any, markNotificationAsRead as any);
router.post('/:tenantId/reminders/send', authenticate as any, authorize(UserRole.ADMIN) as any, checkTenantAccess as any, sendAppointmentReminders as any);
router.post('/reminders/send-all', authenticate as any, authorize(UserRole.ADMIN) as any, sendAllAppointmentReminders as any);

export default router;
