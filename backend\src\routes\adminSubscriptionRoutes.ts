import express from 'express';
import {
  getAllSubscriptions,
  getSubscriptionAnalytics,
  updateUserSubscription,
  cancelUserSubscription
} from '../controllers/adminSubscriptionController';
import { authenticate, authorize } from '../middleware/auth';
import { asyncHandler } from '../utils/routeHandler';
import { UserRole } from '../models/supabase';

const router = express.Router();

// All routes are protected and require super admin access
router.get('/subscriptions', authenticate, authorize(UserRole.SUPER_ADMIN), asyncHandler(getAllSubscriptions));
router.get('/analytics', authenticate, authorize(UserRole.SUPER_ADMIN), asyncHandler(getSubscriptionAnalytics));
router.put('/subscriptions/:subscriptionId', authenticate, authorize(UserRole.SUPER_ADMIN), asyncHandler(updateUserSubscription));
router.post('/subscriptions/:subscriptionId/cancel', authenticate, authorize(UserRole.SUPER_ADMIN), asyncHandler(cancelUserSubscription));

export default router;
