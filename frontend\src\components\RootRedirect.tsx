import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { UserRole } from '../services/userService';

const RootRedirect: React.FC = () => {
  const { isAuthenticated, user, isLoading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!isLoading && isAuthenticated && user) {
      // Redirect based on user role
      switch (user.role) {
        case UserRole.SUPER_ADMIN:
          navigate('/super-admin/dashboard', { replace: true });
          break;
        case UserRole.ADMIN:
        case UserRole.STAFF:
          navigate('/dashboard', { replace: true });
          break;
        case UserRole.CLIENT:
          navigate('/my-appointments', { replace: true });
          break;
        default:
          // Default fallback
          navigate('/dashboard', { replace: true });
      }
    }
  }, [isAuthenticated, user, isLoading, navigate]);

  // Return null as this is just a redirect component
  return null;
};

export default RootRedirect;
